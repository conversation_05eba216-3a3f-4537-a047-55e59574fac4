"""
Issue detection system for payroll documents and HR complaints.
Detects common salary-related problems and inconsistencies.
"""

import logging
import re
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor, as_completed

from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

from ..utils.logger import get_logger

logger = get_logger(__name__)

# Global thread pool for parallel processing
_executor = ThreadPoolExecutor(max_workers=4)

@dataclass
class DetectedIssue:
    """Represents a detected issue in payroll documents or complaints."""
    issue_type: str
    description: str
    confidence: float
    severity: str  # low, medium, high, critical
    affected_fields: List[str]
    suggested_action: str
    evidence: Dict[str, Any]


class PayrollIssueDetector:
    """
    Detects issues in payroll documents and classifies HR complaints.
    Optimized for speed with async support and caching.
    """
    
    # Issue type definitions with patterns and thresholds
    ISSUE_PATTERNS = {
        'bonus_missing': {
            'keywords': ['bonus', 'incentive', 'performance bonus', 'annual bonus', 'missing bonus'],
            'patterns': [
                r'(?:no|missing|where is|not received).*bonus',
                r'bonus.*(?:not|missing|absent)',
                r'expected.*bonus.*not.*received'
            ],
            'severity': 'medium'
        },
        'tax_error': {
            'keywords': ['tax', 'tds', 'income tax', 'wrong tax', 'tax calculation'],
            'patterns': [
                r'(?:wrong|incorrect|error).*tax',
                r'tax.*(?:calculation|computed|deducted).*(?:wrong|error)',
                r'tds.*(?:incorrect|wrong|error)'
            ],
            'severity': 'high'
        },
        'salary_delay': {
            'keywords': ['salary delay', 'late salary', 'payment delay', 'not received salary'],
            'patterns': [
                r'salary.*(?:delayed|late|not received)',
                r'(?:delayed|late).*salary',
                r'payment.*(?:delayed|late|pending)'
            ],
            'severity': 'high'
        },
        'ctc_mismatch': {
            'keywords': ['ctc mismatch', 'salary mismatch', 'wrong ctc', 'ctc difference'],
            'patterns': [
                r'ctc.*(?:mismatch|different|wrong)',
                r'salary.*(?:mismatch|different|wrong)',
                r'expected.*ctc.*different'
            ],
            'severity': 'high'
        },
        'deduction_error': {
            'keywords': ['wrong deduction', 'deduction error', 'pf error', 'esi error'],
            'patterns': [
                r'(?:wrong|incorrect|error).*deduction',
                r'deduction.*(?:wrong|incorrect|error)',
                r'(?:pf|esi|professional tax).*(?:wrong|error|incorrect)'
            ],
            'severity': 'medium'
        },
        'leave_balance_error': {
            'keywords': ['leave balance', 'leave error', 'wrong leave', 'leave calculation'],
            'patterns': [
                r'leave.*(?:balance|calculation).*(?:wrong|error)',
                r'(?:wrong|incorrect).*leave.*balance',
                r'leave.*not.*updated'
            ],
            'severity': 'medium'
        },
        'overtime_missing': {
            'keywords': ['overtime', 'ot', 'extra hours', 'overtime pay'],
            'patterns': [
                r'overtime.*(?:not|missing|absent)',
                r'(?:no|missing).*overtime',
                r'extra hours.*not.*paid'
            ],
            'severity': 'medium'
        },
        'allowance_missing': {
            'keywords': ['allowance', 'per diem', 'travel allowance', 'missing allowance'],
            'patterns': [
                r'allowance.*(?:not|missing|absent)',
                r'(?:no|missing).*allowance',
                r'travel.*allowance.*not.*paid'
            ],
            'severity': 'medium'
        },
        'payslip_error': {
            'keywords': ['payslip error', 'wrong payslip', 'payslip issue'],
            'patterns': [
                r'payslip.*(?:error|wrong|issue)',
                r'(?:error|wrong).*payslip',
                r'payslip.*not.*correct'
            ],
            'severity': 'high'
        },
        'reimbursement_pending': {
            'keywords': ['reimbursement', 'expense claim', 'pending reimbursement'],
            'patterns': [
                r'reimbursement.*(?:pending|not.*paid)',
                r'expense.*claim.*pending',
                r'bill.*not.*reimbursed'
            ],
            'severity': 'medium'
        }
    }
    
    def __init__(self, embeddings_model: str = "BAAI/bge-base-en-v1.5"):
        """
        Initialize the issue detector.
        
        Args:
            embeddings_model: Name of the sentence transformer model for semantic similarity
        """
        self.embeddings_model_name = embeddings_model
        self.embeddings_model = None
        self.issue_embeddings = {}
        self._load_embeddings_model()
        
        # Pre-compute embeddings for issue patterns
        self._precompute_issue_embeddings()
        
        # Cache for document metadata
        self._document_cache = {}
        self._cache_ttl = 300  # 5 minutes
    
    def _load_embeddings_model(self):
        """Load the sentence transformer model."""
        try:
            logger.info(f"Loading embeddings model: {self.embeddings_model_name}")
            self.embeddings_model = SentenceTransformer(self.embeddings_model_name)
            logger.info("Embeddings model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load embeddings model: {e}")
            raise
    
    def _precompute_issue_embeddings(self):
        """Pre-compute embeddings for issue patterns for faster similarity matching."""
        self.issue_embeddings = {}
        
        for issue_type, config in self.ISSUE_PATTERNS.items():
            # Combine keywords and patterns for embedding
            text_samples = config['keywords'] + [
                pattern.replace(r'(?:', '').replace(r'.*', ' ').replace(r'\?', '')
                for pattern in config['patterns']
            ]
            
            # Generate embeddings
            embeddings = self.embeddings_model.encode(text_samples)
            self.issue_embeddings[issue_type] = embeddings
        
        logger.info("Pre-computed embeddings for issue detection")
    
    async def detect_document_issues_async(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """
        Async version of detect_document_issues for better performance.
        
        Args:
            structured_data: Structured payroll data from entity extraction
            
        Returns:
            List of detected issues
        """
        # Run all detection methods in parallel
        tasks = [
            self._check_validation_flags_async(structured_data),
            self._check_missing_fields_async(structured_data),
            self._check_salary_calculations_async(structured_data),
            self._check_value_anomalies_async(structured_data)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Combine all issues
        all_issues = []
        for issues in results:
            all_issues.extend(issues)
        
        logger.info(f"Detected {len(all_issues)} issues in document")
        return all_issues
    
    def detect_document_issues(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """
        Detect issues in structured payroll document data.
        
        Args:
            structured_data: Structured payroll data from entity extraction
            
        Returns:
            List of detected issues
        """
        # Use async version if in async context, otherwise fallback to sync
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, use sync version
                return self._detect_document_issues_sync(structured_data)
            else:
                # We can use async
                return loop.run_until_complete(self.detect_document_issues_async(structured_data))
        except RuntimeError:
            # No event loop, use sync version
            return self._detect_document_issues_sync(structured_data)
    
    def _detect_document_issues_sync(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Synchronous version for compatibility."""
        issues = []
        
        # Check for validation flags from entity extraction
        validation_flags = structured_data.get('validation_flags', [])
        for flag in validation_flags:
            issue = self._create_issue_from_validation_flag(flag)
            if issue:
                issues.append(issue)
        
        # Check for missing critical fields
        missing_field_issues = self._check_missing_fields(structured_data)
        issues.extend(missing_field_issues)
        
        # Check for salary calculation inconsistencies
        calculation_issues = self._check_salary_calculations(structured_data)
        issues.extend(calculation_issues)
        
        # Check for unusual values
        anomaly_issues = self._check_value_anomalies(structured_data)
        issues.extend(anomaly_issues)
        
        logger.info(f"Detected {len(issues)} issues in document")
        return issues
    
    async def _check_validation_flags_async(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Async validation flag checking."""
        validation_flags = structured_data.get('validation_flags', [])
        issues = []
        
        for flag in validation_flags:
            issue = self._create_issue_from_validation_flag(flag)
            if issue:
                issues.append(issue)
        
        return issues
    
    async def _check_missing_fields_async(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Async missing fields checking."""
        return await asyncio.get_event_loop().run_in_executor(
            _executor, self._check_missing_fields, structured_data
        )
    
    async def _check_salary_calculations_async(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Async salary calculations checking."""
        return await asyncio.get_event_loop().run_in_executor(
            _executor, self._check_salary_calculations, structured_data
        )
    
    async def _check_value_anomalies_async(self, structured_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Async value anomalies checking."""
        return await asyncio.get_event_loop().run_in_executor(
            _executor, self._check_value_anomalies, structured_data
        )
    
    async def classify_complaint_async(self, complaint_text: str, document_data: Optional[Dict[str, Any]] = None) -> List[DetectedIssue]:
        """
        Async version of classify_complaint for better performance.
        
        Args:
            complaint_text: Text of the complaint
            document_data: Optional related document data for context
            
        Returns:
            List of detected issues with confidence scores
        """
        # Run detection methods in parallel
        tasks = [
            self._detect_with_regex_async(complaint_text),
            self._detect_with_semantics_async(complaint_text)
        ]
        
        if document_data:
            tasks.append(self._analyze_with_context_async(complaint_text, document_data))
        
        results = await asyncio.gather(*tasks)
        
        # Combine all issues
        all_issues = []
        for issues in results:
            all_issues.extend(issues)
        
        # Remove duplicates and rank
        unique_issues = self._deduplicate_and_rank(all_issues)
        
        logger.info(f"Classified complaint with {len(unique_issues)} issue types")
        return unique_issues
    
    def classify_complaint(self, complaint_text: str, document_data: Optional[Dict[str, Any]] = None) -> List[DetectedIssue]:
        """
        Classify and analyze an HR complaint.
        
        Args:
            complaint_text: Text of the complaint
            document_data: Optional related document data for context
            
        Returns:
            List of detected issues with confidence scores
        """
        # Use async version if possible
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, use sync version
                return self._classify_complaint_sync(complaint_text, document_data)
            else:
                # We can use async
                return loop.run_until_complete(self.classify_complaint_async(complaint_text, document_data))
        except RuntimeError:
            # No event loop, use sync version
            return self._classify_complaint_sync(complaint_text, document_data)
    
    def _classify_complaint_sync(self, complaint_text: str, document_data: Optional[Dict[str, Any]] = None) -> List[DetectedIssue]:
        """Synchronous version for compatibility."""
        issues = []
        
        # Regex-based pattern matching
        regex_issues = self._detect_with_regex(complaint_text)
        issues.extend(regex_issues)
        
        # Semantic similarity matching
        semantic_issues = self._detect_with_semantics(complaint_text)
        issues.extend(semantic_issues)
        
        # Context-based analysis if document data is available
        if document_data:
            context_issues = self._analyze_with_context(complaint_text, document_data)
            issues.extend(context_issues)
        
        # Remove duplicates and rank
        unique_issues = self._deduplicate_and_rank(issues)
        
        logger.info(f"Classified complaint with {len(unique_issues)} issue types")
        return unique_issues
    
    async def _detect_with_regex_async(self, text: str) -> List[DetectedIssue]:
        """Async regex detection."""
        return await asyncio.get_event_loop().run_in_executor(
            _executor, self._detect_with_regex, text
        )
    
    async def _detect_with_semantics_async(self, text: str) -> List[DetectedIssue]:
        """Async semantic detection."""
        return await asyncio.get_event_loop().run_in_executor(
            _executor, self._detect_with_semantics, text
        )
    
    async def _analyze_with_context_async(self, complaint_text: str, document_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Async context analysis."""
        return await asyncio.get_event_loop().run_in_executor(
            _executor, self._analyze_with_context, complaint_text, document_data
        )
    
    def _create_issue_from_validation_flag(self, flag: Dict[str, Any]) -> Optional[DetectedIssue]:
        """Create an issue from a validation flag."""
        field = flag.get('field', '')
        issue_type = flag.get('issue', '')
        confidence = flag.get('confidence', 0.5)
        
        if issue_type == 'salary_mismatch':
            return DetectedIssue(
                issue_type='ctc_mismatch',
                description=f"Salary calculation mismatch detected in {field}",
                confidence=confidence,
                severity='high',
                affected_fields=[field],
                suggested_action="Review salary calculations and verify deductions",
                evidence=flag
            )
        elif issue_type == 'date_mismatch':
            return DetectedIssue(
                issue_type='payslip_error',
                description=f"Date inconsistency detected in {field}",
                confidence=confidence,
                severity='medium',
                affected_fields=[field],
                suggested_action="Verify pay period and payment dates",
                evidence=flag
            )
        
        return None
    
    def _check_missing_fields(self, data: Dict[str, Any]) -> List[DetectedIssue]:
        """Check for missing critical fields."""
        issues = []
        critical_fields = ['employee_id', 'employee_name', 'gross_pay', 'net_pay']
        
        missing_fields = [field for field in critical_fields if not data.get(field)]
        
        if missing_fields:
            issues.append(DetectedIssue(
                issue_type='payslip_error',
                description=f"Missing critical fields: {', '.join(missing_fields)}",
                confidence=0.9,
                severity='high',
                affected_fields=missing_fields,
                suggested_action="Ensure all critical payroll fields are properly extracted",
                evidence={'missing_fields': missing_fields}
            ))
        
        return issues
    
    def _check_salary_calculations(self, data: Dict[str, Any]) -> List[DetectedIssue]:
        """Check for salary calculation errors."""
        issues = []
        
        gross_pay = data.get('gross_pay')
        net_pay = data.get('net_pay')
        
        if gross_pay and net_pay:
            # Calculate total deductions
            deductions = 0
            deduction_fields = ['tax', 'pf', 'esi', 'professional_tax']
            
            for field in deduction_fields:
                if data.get(field):
                    deductions += data[field]
            
            # Check if gross - deductions = net (with 5% tolerance)
            expected_net = gross_pay - deductions
            tolerance = gross_pay * 0.05
            
            if abs(expected_net - net_pay) > tolerance:
                issues.append(DetectedIssue(
                    issue_type='ctc_mismatch',
                    description=f"Salary calculation error: Expected net pay {expected_net:.2f}, actual {net_pay:.2f}",
                    confidence=0.8,
                    severity='high',
                    affected_fields=['gross_pay', 'net_pay'] + deduction_fields,
                    suggested_action="Review salary calculations and verify all deductions",
                    evidence={
                        'gross_pay': gross_pay,
                        'net_pay': net_pay,
                        'expected_net': expected_net,
                        'total_deductions': deductions
                    }
                ))
        
        return issues
    
    def _check_value_anomalies(self, data: Dict[str, Any]) -> List[DetectedIssue]:
        """Check for unusual or anomalous values."""
        issues = []
        
        # Check for unusually high or low salaries
        gross_pay = data.get('gross_pay')
        if gross_pay:
            if gross_pay < 10000:  # Very low salary
                issues.append(DetectedIssue(
                    issue_type='payslip_error',
                    description=f"Unusually low gross pay: ₹{gross_pay}",
                    confidence=0.7,
                    severity='medium',
                    affected_fields=['gross_pay'],
                    suggested_action="Verify if this is a partial month salary or data entry error",
                    evidence={'gross_pay': gross_pay}
                ))
            elif gross_pay > 1000000:  # Very high salary
                issues.append(DetectedIssue(
                    issue_type='payslip_error',
                    description=f"Unusually high gross pay: ₹{gross_pay}",
                    confidence=0.6,
                    severity='low',
                    affected_fields=['gross_pay'],
                    suggested_action="Verify if this includes special bonuses or annual components",
                    evidence={'gross_pay': gross_pay}
                ))
        
        # Check for negative values
        numeric_fields = ['gross_pay', 'net_pay', 'basic_salary', 'hra', 'tax', 'pf', 'esi', 'bonus']
        for field in numeric_fields:
            value = data.get(field)
            if value is not None and value < 0:
                issues.append(DetectedIssue(
                    issue_type='payslip_error',
                    description=f"Negative value detected in {field}: {value}",
                    confidence=0.9,
                    severity='high',
                    affected_fields=[field],
                    suggested_action=f"Correct the negative value in {field}",
                    evidence={field: value}
                ))
        
        return issues

    def _detect_with_regex(self, text: str) -> List[DetectedIssue]:
        """Detect issues using regex patterns."""
        issues = []
        text_lower = text.lower()

        for issue_type, config in self.ISSUE_PATTERNS.items():
            # Check keyword matches
            keyword_matches = sum(1 for keyword in config['keywords'] if keyword.lower() in text_lower)
            keyword_score = keyword_matches / len(config['keywords'])

            # Check regex pattern matches
            pattern_matches = 0
            for pattern in config['patterns']:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    pattern_matches += 1

            pattern_score = pattern_matches / len(config['patterns']) if config['patterns'] else 0

            # Combine scores
            confidence = (keyword_score * 0.4 + pattern_score * 0.6)

            if confidence > 0.3:  # Threshold for regex detection
                issues.append(DetectedIssue(
                    issue_type=issue_type,
                    description=f"Detected {issue_type.replace('_', ' ')} based on text patterns",
                    confidence=confidence,
                    severity=config['severity'],
                    affected_fields=[],
                    suggested_action=self._get_suggested_action(issue_type),
                    evidence={
                        'keyword_matches': keyword_matches,
                        'pattern_matches': pattern_matches,
                        'detection_method': 'regex'
                    }
                ))

        return issues

    def _detect_with_semantics(self, text: str) -> List[DetectedIssue]:
        """Detect issues using semantic similarity."""
        issues = []

        # Generate embedding for the input text
        text_embedding = self.embeddings_model.encode([text])

        for issue_type, issue_embeddings in self.issue_embeddings.items():
            # Calculate similarity with pre-computed embeddings
            similarities = cosine_similarity(text_embedding, issue_embeddings)[0]
            max_similarity = np.max(similarities)

            if max_similarity > 0.5:  # Threshold for semantic similarity
                config = self.ISSUE_PATTERNS[issue_type]
                issues.append(DetectedIssue(
                    issue_type=issue_type,
                    description=f"Detected {issue_type.replace('_', ' ')} based on semantic similarity",
                    confidence=float(max_similarity),
                    severity=config['severity'],
                    affected_fields=[],
                    suggested_action=self._get_suggested_action(issue_type),
                    evidence={
                        'max_similarity': float(max_similarity),
                        'detection_method': 'semantic'
                    }
                ))

        return issues

    def _analyze_with_context(self, complaint_text: str, document_data: Dict[str, Any]) -> List[DetectedIssue]:
        """Analyze complaint with document context."""
        issues = []

        # Check if complaint mentions specific values that don't match document
        complaint_lower = complaint_text.lower()

        # Extract numbers from complaint
        numbers_in_complaint = re.findall(r'\d+(?:,\d{3})*(?:\.\d{2})?', complaint_text)

        for number_str in numbers_in_complaint:
            try:
                number = float(number_str.replace(',', ''))

                # Check if this number matches any salary component
                salary_fields = ['gross_pay', 'net_pay', 'basic_salary', 'hra', 'tax', 'pf', 'bonus']

                for field in salary_fields:
                    doc_value = document_data.get(field)
                    if doc_value and abs(doc_value - number) < 100:  # Close match
                        # This suggests the complaint is about this specific field
                        if any(keyword in complaint_lower for keyword in ['wrong', 'incorrect', 'error', 'mismatch']):
                            issues.append(DetectedIssue(
                                issue_type='payslip_error',
                                description=f"Complaint mentions {field} value {number} which matches document but indicates error",
                                confidence=0.7,
                                severity='medium',
                                affected_fields=[field],
                                suggested_action=f"Review and verify {field} calculation",
                                evidence={
                                    'mentioned_value': number,
                                    'document_value': doc_value,
                                    'field': field
                                }
                            ))
            except ValueError:
                continue

        return issues

    def _deduplicate_and_rank(self, issues: List[DetectedIssue]) -> List[DetectedIssue]:
        """Remove duplicate issues and rank by confidence."""
        # Group by issue type
        issue_groups = {}
        for issue in issues:
            if issue.issue_type not in issue_groups:
                issue_groups[issue.issue_type] = []
            issue_groups[issue.issue_type].append(issue)

        # Keep the highest confidence issue for each type
        unique_issues = []
        for issue_type, group in issue_groups.items():
            best_issue = max(group, key=lambda x: x.confidence)
            unique_issues.append(best_issue)

        # Sort by confidence (highest first)
        unique_issues.sort(key=lambda x: x.confidence, reverse=True)

        return unique_issues

    def _get_suggested_action(self, issue_type: str) -> str:
        """Get suggested action for an issue type."""
        actions = {
            'bonus_missing': "Review bonus eligibility and payment records. Contact payroll team if bonus is due.",
            'tax_error': "Verify tax calculations with HR. Check TDS certificates and Form 16.",
            'salary_delay': "Contact payroll team immediately. Check bank account details and payment processing.",
            'ctc_mismatch': "Compare offer letter with payslip. Escalate to HR for CTC verification.",
            'deduction_error': "Review deduction calculations. Verify PF, ESI, and tax deduction rates.",
            'leave_balance_error': "Check leave management system. Verify leave applications and approvals.",
            'overtime_missing': "Submit overtime records to supervisor. Ensure proper approval and documentation.",
            'allowance_missing': "Verify allowance eligibility. Check with HR for policy clarification.",
            'payslip_error': "Report to payroll team for correction. Provide supporting documentation.",
            'reimbursement_pending': "Follow up with finance team. Ensure all required documents are submitted."
        }

        return actions.get(issue_type, "Contact HR team for assistance and clarification.")

    def get_issue_statistics(self, issues: List[DetectedIssue]) -> Dict[str, Any]:
        """Get statistics about detected issues."""
        if not issues:
            return {"total_issues": 0}

        # Count by type
        type_counts = {}
        for issue in issues:
            type_counts[issue.issue_type] = type_counts.get(issue.issue_type, 0) + 1

        # Count by severity
        severity_counts = {}
        for issue in issues:
            severity_counts[issue.severity] = severity_counts.get(issue.severity, 0) + 1

        # Average confidence
        avg_confidence = sum(issue.confidence for issue in issues) / len(issues)

        # High confidence issues (>0.7)
        high_confidence_issues = [issue for issue in issues if issue.confidence > 0.7]

        return {
            "total_issues": len(issues),
            "type_counts": type_counts,
            "severity_counts": severity_counts,
            "average_confidence": avg_confidence,
            "high_confidence_count": len(high_confidence_issues),
            "most_common_type": max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else None
        }
