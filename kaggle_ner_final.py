#!/usr/bin/env python3
"""
🚀 KAGGLE NER TRAINING - BULLETPROOF VERSION
Uses standard Transformers library (no SpanMarker)
Based on your exact data format from entity_training_data.jsonl
"""

import os
import sys
import subprocess
import json
import logging
import warnings
import time
import gc
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_DISABLED"] = "true"

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def install_packages():
    """Install required packages."""
    logger.info("🔧 Installing packages...")
    packages = [
        "transformers>=4.30.0",
        "datasets>=2.10.0", 
        "torch>=1.13.0",
        "scikit-learn>=1.2.0",
        "accelerate>=0.20.0",
        "seqeval"
    ]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package, "--quiet"], 
                         capture_output=True, check=True)
            logger.info(f"✅ {package}")
        except:
            logger.warning(f"⚠️ {package} - continuing anyway")

# Install first
install_packages()

# Import after installation
try:
    import torch
    import numpy as np
    from datasets import Dataset, DatasetDict
    from transformers import (
        AutoTokenizer, AutoModelForTokenClassification,
        TrainingArguments, Trainer, DataCollatorForTokenClassification
    )
    from sklearn.model_selection import train_test_split
    from seqeval.metrics import classification_report, f1_score, precision_score, recall_score
    logger.info("✅ All imports successful")
except ImportError as e:
    logger.error(f"❌ Import failed: {e}")
    raise

def create_your_training_data():
    """Create training data using YOUR EXACT format."""
    logger.info("📝 Creating training data from your format...")
    
    # Your actual data from entity_training_data.jsonl
    samples = [
        {"text": "My retention payout for July 2025 was higher than it should be.", "entities": [[3, 19, "salary_component"], [24, 28, "month"], [29, 33, "year"]]},
        {"text": "I need clarification on my annual salary.", "entities": [[27, 40, "salary_type"]]},
        {"text": "My August 2024 mobile phone repair was rejected.", "entities": [[15, 35, "expense_type"], [3, 9, "month"], [10, 14, "year"], [39, 47, "issue_type"]]},
        {"text": "My income tax was not deducted for May 2025.", "entities": [[3, 13, "salary_component"], [18, 30, "issue_type"], [35, 38, "month"], [39, 43, "year"]]},
        {"text": "My March 2024 earned leave payout still uncredited.", "entities": [[10, 28, "salary_component"], [3, 8, "month"], [9, 13, "year"], [40, 50, "issue_type"]]},
        {"text": "The overtime payment for June 2024 is missing.", "entities": [[4, 20, "salary_component"], [25, 29, "month"], [30, 34, "year"], [38, 45, "issue_type"]]},
        {"text": "My basic salary for April 2025 needs verification.", "entities": [[3, 15, "salary_component"], [20, 25, "month"], [26, 30, "year"]]},
        {"text": "The bonus amount for December 2024 was incorrect.", "entities": [[4, 16, "salary_component"], [21, 29, "month"], [30, 34, "year"], [39, 48, "issue_type"]]},
        {"text": "My travel allowance for September 2024 is pending.", "entities": [[3, 19, "salary_component"], [24, 33, "month"], [34, 38, "year"], [42, 49, "issue_type"]]},
        {"text": "The medical reimbursement for October 2024 was denied.", "entities": [[4, 25, "expense_type"], [30, 37, "month"], [38, 42, "year"], [47, 53, "issue_type"]]},
    ]
    
    # Expand dataset with variations
    expanded = []
    months = ["January", "February", "March", "April", "May", "June", 
              "July", "August", "September", "October", "November", "December"]
    years = ["2023", "2024", "2025"]
    components = ["basic salary", "overtime pay", "bonus", "allowance", "deduction", "payout"]
    expenses = ["travel expense", "medical bill", "phone repair", "internet cost"]
    issues = ["pending", "delayed", "missing", "incorrect", "denied", "rejected"]
    
    # Add original samples
    expanded.extend(samples)
    
    # Generate more samples
    templates = [
        "My {component} for {month} {year} is {issue}.",
        "The {component} for {month} {year} needs review.",
        "My {expense} for {month} {year} was {issue}.",
        "Please check my {component} for {month} {year}.",
        "The {expense} claim for {month} {year} is {issue}.",
    ]
    
    for i in range(100):  # Generate 100 more samples
        template = templates[i % len(templates)]
        month = months[i % len(months)]
        year = years[i % len(years)]
        
        if "{component}" in template:
            component = components[i % len(components)]
            issue = issues[i % len(issues)]
            text = template.format(component=component, month=month, year=year, issue=issue)
            
            entities = []
            # Find positions
            comp_pos = text.find(component)
            if comp_pos >= 0:
                entities.append([comp_pos, comp_pos + len(component), "salary_component"])
            
            month_pos = text.find(month)
            if month_pos >= 0:
                entities.append([month_pos, month_pos + len(month), "month"])
            
            year_pos = text.find(year)
            if year_pos >= 0:
                entities.append([year_pos, year_pos + len(year), "year"])
            
            issue_pos = text.find(issue)
            if issue_pos >= 0:
                entities.append([issue_pos, issue_pos + len(issue), "issue_type"])
            
            expanded.append({"text": text, "entities": entities})
        
        elif "{expense}" in template:
            expense = expenses[i % len(expenses)]
            issue = issues[i % len(issues)]
            text = template.format(expense=expense, month=month, year=year, issue=issue)
            
            entities = []
            exp_pos = text.find(expense)
            if exp_pos >= 0:
                entities.append([exp_pos, exp_pos + len(expense), "expense_type"])
            
            month_pos = text.find(month)
            if month_pos >= 0:
                entities.append([month_pos, month_pos + len(month), "month"])
            
            year_pos = text.find(year)
            if year_pos >= 0:
                entities.append([year_pos, year_pos + len(year), "year"])
            
            issue_pos = text.find(issue)
            if issue_pos >= 0:
                entities.append([issue_pos, issue_pos + len(issue), "issue_type"])
            
            expanded.append({"text": text, "entities": entities})
    
    logger.info(f"✅ Created {len(expanded)} training samples")
    return expanded

def convert_to_bio_tags(text: str, entities: List[List]) -> Tuple[List[str], List[str]]:
    """Convert text and entities to BIO format."""
    # Simple word tokenization
    words = text.split()
    bio_tags = ["O"] * len(words)
    
    # Calculate word positions
    word_positions = []
    char_pos = 0
    for word in words:
        start = text.find(word, char_pos)
        end = start + len(word)
        word_positions.append((start, end))
        char_pos = end
    
    # Assign BIO tags
    for entity in entities:
        if len(entity) >= 3:
            start, end, label = entity[0], entity[1], entity[2]
            
            # Find overlapping words
            entity_words = []
            for i, (word_start, word_end) in enumerate(word_positions):
                if word_start < end and word_end > start:
                    entity_words.append(i)
            
            # Assign B- and I- tags
            for idx, word_idx in enumerate(entity_words):
                if bio_tags[word_idx] == "O":  # Don't overwrite existing tags
                    if idx == 0:
                        bio_tags[word_idx] = f"B-{label}"
                    else:
                        bio_tags[word_idx] = f"I-{label}"
    
    return words, bio_tags

def prepare_dataset(samples):
    """Prepare dataset for training."""
    logger.info("🔄 Converting to BIO format...")
    
    processed_samples = []
    for sample in samples:
        words, bio_tags = convert_to_bio_tags(sample["text"], sample["entities"])
        if words and bio_tags and len(words) == len(bio_tags):
            processed_samples.append({
                "tokens": words,
                "ner_tags": bio_tags
            })
    
    # Get unique labels
    all_labels = set()
    for sample in processed_samples:
        all_labels.update(sample["ner_tags"])
    
    labels = sorted(list(all_labels))
    label2id = {label: i for i, label in enumerate(labels)}
    id2label = {i: label for i, label in enumerate(labels)}
    
    logger.info(f"📊 Labels: {labels}")
    logger.info(f"📊 Processed {len(processed_samples)} samples")
    
    return processed_samples, labels, label2id, id2label

def tokenize_and_align_labels(examples, tokenizer, label2id):
    """Tokenize and align labels."""
    tokenized_inputs = tokenizer(
        examples["tokens"], 
        truncation=True, 
        is_split_into_words=True,
        max_length=128,
        padding=True
    )
    
    labels = []
    for i, label in enumerate(examples["ner_tags"]):
        word_ids = tokenized_inputs.word_ids(batch_index=i)
        previous_word_idx = None
        label_ids = []
        
        for word_idx in word_ids:
            if word_idx is None:
                label_ids.append(-100)
            elif word_idx != previous_word_idx:
                label_ids.append(label2id[label[word_idx]])
            else:
                label_ids.append(-100)
            previous_word_idx = word_idx
        
        labels.append(label_ids)
    
    tokenized_inputs["labels"] = labels
    return tokenized_inputs

def compute_metrics(eval_pred, id2label):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=2)
    
    true_predictions = [
        [id2label[p] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    true_labels = [
        [id2label[l] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    
    results = {
        "precision": precision_score(true_labels, true_predictions),
        "recall": recall_score(true_labels, true_predictions),
        "f1": f1_score(true_labels, true_predictions),
    }
    return results

def train_ner_model():
    """Main training function."""
    logger.info("🚀 Starting NER Training")
    
    try:
        # Step 1: Create training data
        raw_samples = create_your_training_data()
        
        # Step 2: Prepare dataset
        processed_samples, labels, label2id, id2label = prepare_dataset(raw_samples)
        
        if len(processed_samples) < 10:
            raise ValueError(f"Not enough samples: {len(processed_samples)}")
        
        # Step 3: Split data
        train_samples, eval_samples = train_test_split(
            processed_samples, test_size=0.2, random_state=42
        )
        
        logger.info(f"📊 Train: {len(train_samples)}, Eval: {len(eval_samples)}")
        
        # Step 4: Create datasets
        train_dataset = Dataset.from_list(train_samples)
        eval_dataset = Dataset.from_list(eval_samples)
        
        # Step 5: Initialize tokenizer and model
        model_name = "distilbert-base-uncased"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForTokenClassification.from_pretrained(
            model_name, 
            num_labels=len(labels),
            id2label=id2label,
            label2id=label2id
        )
        
        logger.info(f"🤖 Model: {model_name}")
        logger.info(f"🏷️ Labels: {len(labels)}")
        
        # Step 6: Tokenize datasets
        train_dataset = train_dataset.map(
            lambda x: tokenize_and_align_labels(x, tokenizer, label2id),
            batched=True
        )
        eval_dataset = eval_dataset.map(
            lambda x: tokenize_and_align_labels(x, tokenizer, label2id),
            batched=True
        )
        
        # Step 7: Setup training
        output_dir = "/kaggle/working/ner_model"
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            learning_rate=2e-5,
            per_device_train_batch_size=8,
            per_device_eval_batch_size=8,
            num_train_epochs=3,
            weight_decay=0.01,
            eval_strategy="epoch",
            save_strategy="epoch",
            logging_steps=10,
            load_best_model_at_end=True,
            metric_for_best_model="f1",
            greater_is_better=True,
            report_to="none",
            remove_unused_columns=False,
        )
        
        # Step 8: Create trainer
        data_collator = DataCollatorForTokenClassification(tokenizer)
        
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=tokenizer,
            data_collator=data_collator,
            compute_metrics=lambda x: compute_metrics(x, id2label),
        )
        
        # Step 9: Train
        logger.info("🏋️ Training...")
        start_time = time.time()
        trainer.train()
        training_time = time.time() - start_time
        
        # Step 10: Save
        trainer.save_model(output_dir)
        tokenizer.save_pretrained(output_dir)
        
        # Save config
        config = {
            "labels": labels,
            "label2id": label2id,
            "id2label": id2label,
            "model_name": model_name,
            "training_samples": len(train_samples),
            "eval_samples": len(eval_samples),
            "training_time": training_time
        }
        
        with open(f"{output_dir}/config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        # Step 11: Final evaluation
        eval_results = trainer.evaluate()
        
        # Success!
        print("\n" + "="*60)
        print("🎉 NER TRAINING COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"📂 Model saved to: {output_dir}")
        print(f"🏷️ Labels: {len(labels)}")
        print(f"📊 Training samples: {len(train_samples)}")
        print(f"⏱️ Training time: {training_time:.1f}s")
        print(f"📈 F1 Score: {eval_results.get('eval_f1', 0):.3f}")
        print(f"📈 Precision: {eval_results.get('eval_precision', 0):.3f}")
        print(f"📈 Recall: {eval_results.get('eval_recall', 0):.3f}")
        print("="*60)
        
        return {
            "success": True,
            "model_path": output_dir,
            "config": config,
            "eval_results": eval_results
        }
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("🚀 STARTING BULLETPROOF NER TRAINING")
    print("="*60)
    
    result = train_ner_model()
    
    if result["success"]:
        print("\n✅ SUCCESS! Your NER model is ready!")
    else:
        print(f"\n❌ FAILED: {result['error']}")
