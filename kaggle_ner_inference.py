#!/usr/bin/env python3
"""
🚀 KAGGLE NER INFERENCE - Test your trained model
"""

import json
import torch
from transformers import AutoTokenizer, AutoModelForTokenClassification
import numpy as np

def load_trained_model(model_path="/kaggle/working/ner_model"):
    """Load the trained NER model."""
    print(f"📂 Loading model from {model_path}")
    
    # Load config
    with open(f"{model_path}/config.json", "r") as f:
        config = json.load(f)
    
    # Load model and tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForTokenClassification.from_pretrained(model_path)
    
    print(f"✅ Model loaded successfully")
    print(f"🏷️ Labels: {config['labels']}")
    
    return model, tokenizer, config

def predict_entities(text, model, tokenizer, config):
    """Predict entities in text."""
    print(f"🔍 Analyzing: '{text}'")
    
    # Tokenize
    inputs = tokenizer(text, return_tensors="pt", truncation=True, max_length=128)
    
    # Predict
    with torch.no_grad():
        outputs = model(**inputs)
        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
        predicted_token_class_ids = predictions.argmax(-1)
    
    # Convert to labels
    tokens = tokenizer.convert_ids_to_tokens(inputs["input_ids"][0])
    predicted_labels = [config["id2label"][str(id.item())] for id in predicted_token_class_ids[0]]
    
    # Extract entities
    entities = []
    current_entity = None
    
    for i, (token, label) in enumerate(zip(tokens, predicted_labels)):
        if token in ["[CLS]", "[SEP]", "[PAD]"]:
            continue
            
        if label.startswith("B-"):
            if current_entity:
                entities.append(current_entity)
            current_entity = {
                "text": token.replace("##", ""),
                "label": label[2:],
                "start": i,
                "tokens": [token]
            }
        elif label.startswith("I-") and current_entity and label[2:] == current_entity["label"]:
            current_entity["text"] += token.replace("##", "")
            current_entity["tokens"].append(token)
        else:
            if current_entity:
                entities.append(current_entity)
                current_entity = None
    
    if current_entity:
        entities.append(current_entity)
    
    return entities

def test_model():
    """Test the trained model with sample texts."""
    print("🚀 TESTING TRAINED NER MODEL")
    print("="*50)
    
    try:
        # Load model
        model, tokenizer, config = load_trained_model()
        
        # Test sentences
        test_sentences = [
            "My basic salary for March 2025 is pending.",
            "The overtime payment for July 2024 was incorrect.",
            "My travel expense for December 2023 was rejected.",
            "I need clarification on my annual salary.",
            "The bonus amount for June 2025 needs review.",
        ]
        
        print("\n🧪 TESTING PREDICTIONS:")
        print("-" * 50)
        
        for sentence in test_sentences:
            entities = predict_entities(sentence, model, tokenizer, config)
            
            print(f"\n📝 Text: {sentence}")
            if entities:
                print("🏷️ Entities found:")
                for entity in entities:
                    print(f"   • '{entity['text']}' → {entity['label']}")
            else:
                print("🏷️ No entities found")
        
        print("\n" + "="*50)
        print("✅ Testing completed successfully!")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_model()
