"""
Payroll-specific entity extraction with specialized patterns and fallbacks.
Extends the base EntityExtractor for HR/payroll document processing.
"""

import re
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .entity_extractor import EntityExtractor, Entity, EntityExtractionResult
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PayrollEntity(Entity):
    """Extended entity class for payroll-specific information."""
    field_type: str = "general"  # salary, tax, personal, date, etc.
    normalized_value: Optional[str] = None
    currency: Optional[str] = None
    validation_status: str = "pending"  # valid, invalid, needs_review


class PayrollEntityExtractor(EntityExtractor):
    """
    Specialized entity extractor for payroll documents.
    Combines NER with regex patterns and domain-specific validation.
    """
    
    # Payroll-specific entity patterns
    PAYROLL_PATTERNS = {
        'EMPLOYEE_ID': [
            r'(?:Employee\s+ID|EMP\s*ID|Staff\s+ID)[\s:]*([A-Z0-9]{3,15})',
            r'(?:ID|Emp)[\s:]*([A-Z]{2,4}\d{3,8})',
            r'\b([A-Z]{2,4}\d{4,8})\b'  # Generic alphanumeric ID
        ],
        'EMPLOYEE_NAME': [
            r'(?:Employee\s+Name|Name)[\s:]*([A-Za-z\s]{2,50})',
            r'(?:Mr\.|Ms\.|Mrs\.)?\s*([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',
        ],
        'GROSS_PAY': [
            r'(?:Gross\s+Pay|Gross\s+Salary|Total\s+Earnings)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
            r'Gross[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'NET_PAY': [
            r'(?:Net\s+Pay|Net\s+Salary|Take\s+Home)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
            r'Net[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'BASIC_SALARY': [
            r'(?:Basic\s+Salary|Basic\s+Pay|Basic)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'HRA': [
            r'(?:HRA|House\s+Rent\s+Allowance)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'TAX': [
            r'(?:Income\s+Tax|TDS|Tax\s+Deducted)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
            r'(?:Tax)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'PF': [
            r'(?:PF|Provident\s+Fund|EPF)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'ESI': [
            r'(?:ESI|ESIC)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'PROFESSIONAL_TAX': [
            r'(?:Professional\s+Tax|PT|Prof\.\s+Tax)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'BONUS': [
            r'(?:Bonus|Incentive|Performance\s+Bonus)[\s:]*(?:₹|INR|Rs\.?)?\s*([\d,]+\.?\d*)',
        ],
        'MONTH': [
            r'(?:Month|Pay\s+Period|Salary\s+for)[\s:]*([A-Za-z]+\s+\d{4}|\d{1,2}[-/]\d{4})',
            r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*[\s-]*(\d{4})',
        ],
        'PAY_DATE': [
            r'(?:Pay\s+Date|Payment\s+Date|Salary\s+Date)[\s:]*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})',
            r'(?:Date)[\s:]*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})',
        ],
        'ORGANIZATION': [
            r'(?:Company|Organization|Employer)[\s:]*([A-Za-z\s&.,]{3,50})',
            r'^([A-Z][A-Za-z\s&.,]{2,49})(?:\s+(?:Pvt\.?\s+Ltd\.?|Ltd\.?|Inc\.?|Corp\.?))?',
        ]
    }
    
    # Currency patterns
    CURRENCY_PATTERNS = {
        'INR': [r'₹', r'INR', r'Rs\.?', r'Rupees?'],
        'USD': [r'\$', r'USD', r'Dollars?'],
        'EUR': [r'€', r'EUR', r'Euros?'],
    }
    
    # Month name mappings
    MONTH_MAPPING = {
        'january': '01', 'jan': '01',
        'february': '02', 'feb': '02',
        'march': '03', 'mar': '03',
        'april': '04', 'apr': '04',
        'may': '05',
        'june': '06', 'jun': '06',
        'july': '07', 'jul': '07',
        'august': '08', 'aug': '08',
        'september': '09', 'sep': '09', 'sept': '09',
        'october': '10', 'oct': '10',
        'november': '11', 'nov': '11',
        'december': '12', 'dec': '12'
    }
    
    def __init__(self, model_path: Optional[str] = None, confidence_threshold: float = 0.5):
        """
        Initialize payroll entity extractor.
        
        Args:
            model_path: Path to NER model
            confidence_threshold: Minimum confidence for entity extraction
        """
        super().__init__(model_path, confidence_threshold)
        logger.info("PayrollEntityExtractor initialized with regex fallbacks")
    
    def extract_payroll_entities(self, text: str) -> Dict[str, Any]:
        """
        Extract payroll-specific entities with validation and normalization.

        Args:
            text: Input payroll document text

        Returns:
            Dictionary with extracted and normalized payroll data
        """
        start_time = time.time()

        # First, try NER extraction if model is available
        if hasattr(self, 'model') and self.model is not None:
            ner_result = self.extract_entities(text)
            ner_entities = ner_result.entities
            processing_time = ner_result.processing_time
        else:
            # Skip NER if model not available
            ner_entities = []
            processing_time = 0.0

        # Apply regex patterns for payroll-specific fields (always run this)
        regex_entities = self._extract_with_regex(text)

        # Combine and validate results
        combined_entities = self._combine_and_validate(ner_entities, regex_entities)

        # Normalize and structure the data
        structured_data = self._structure_payroll_data(combined_entities, text)

        total_processing_time = time.time() - start_time

        return {
            "entities": combined_entities,
            "structured_data": structured_data,
            "processing_time": max(processing_time, total_processing_time),
            "extraction_methods": ["regex", "validation"] if not ner_entities else ["ner", "regex", "validation"]
        }
    
    def _extract_with_regex(self, text: str) -> List[PayrollEntity]:
        """
        Extract entities using regex patterns.
        
        Args:
            text: Input text
            
        Returns:
            List of PayrollEntity objects
        """
        entities = []
        
        for entity_type, patterns in self.PAYROLL_PATTERNS.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    # Get the captured group (the actual value)
                    if match.groups():
                        value = match.group(1).strip()
                        start_pos = match.start(1)
                        end_pos = match.end(1)
                    else:
                        value = match.group(0).strip()
                        start_pos = match.start()
                        end_pos = match.end()
                    
                    # Skip very short or empty matches
                    if len(value) < 2:
                        continue
                    
                    # Determine field type
                    field_type = self._get_field_type(entity_type)
                    
                    # Extract currency if applicable
                    currency = self._extract_currency(match.group(0)) if field_type == "salary" else None
                    
                    # Normalize value
                    normalized_value = self._normalize_value(value, entity_type)
                    
                    entity = PayrollEntity(
                        text=value,
                        label=entity_type,
                        start=start_pos,
                        end=end_pos,
                        confidence=0.8,  # High confidence for regex matches
                        field_type=field_type,
                        normalized_value=normalized_value,
                        currency=currency
                    )
                    
                    entities.append(entity)
        
        return entities
    
    def _get_field_type(self, entity_type: str) -> str:
        """Categorize entity types into field types."""
        salary_fields = ['GROSS_PAY', 'NET_PAY', 'BASIC_SALARY', 'HRA', 'TAX', 'PF', 'ESI', 'PROFESSIONAL_TAX', 'BONUS']
        date_fields = ['MONTH', 'PAY_DATE']
        personal_fields = ['EMPLOYEE_NAME', 'EMPLOYEE_ID']
        
        if entity_type in salary_fields:
            return "salary"
        elif entity_type in date_fields:
            return "date"
        elif entity_type in personal_fields:
            return "personal"
        else:
            return "general"
    
    def _extract_currency(self, text: str) -> Optional[str]:
        """Extract currency from text."""
        for currency, patterns in self.CURRENCY_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, text):
                    return currency
        return "INR"  # Default to INR for Indian payslips
    
    def _normalize_value(self, value: str, entity_type: str) -> str:
        """Normalize extracted values based on entity type."""
        if entity_type in ['GROSS_PAY', 'NET_PAY', 'BASIC_SALARY', 'HRA', 'TAX', 'PF', 'ESI', 'PROFESSIONAL_TAX', 'BONUS']:
            # Remove commas and convert to float
            cleaned = re.sub(r'[,\s]', '', value)
            try:
                return str(float(cleaned))
            except ValueError:
                return value
        
        elif entity_type == 'MONTH':
            # Normalize month to MM-YYYY format
            return self._normalize_month(value)
        
        elif entity_type == 'PAY_DATE':
            # Normalize date to YYYY-MM-DD format
            return self._normalize_date(value)
        
        elif entity_type in ['EMPLOYEE_NAME', 'ORGANIZATION']:
            # Clean up names and organization names
            return ' '.join(value.split()).title()
        
        elif entity_type == 'EMPLOYEE_ID':
            # Uppercase and remove spaces
            return value.upper().replace(' ', '')
        
        return value
    
    def _normalize_month(self, month_str: str) -> str:
        """Normalize month string to MM-YYYY format."""
        month_str = month_str.lower().strip()
        
        # Try to extract month name and year
        for month_name, month_num in self.MONTH_MAPPING.items():
            if month_name in month_str:
                # Extract year
                year_match = re.search(r'\d{4}', month_str)
                if year_match:
                    return f"{month_num}-{year_match.group()}"
        
        # Try MM/YYYY or MM-YYYY format
        date_match = re.search(r'(\d{1,2})[-/](\d{4})', month_str)
        if date_match:
            month = date_match.group(1).zfill(2)
            year = date_match.group(2)
            return f"{month}-{year}"
        
        return month_str
    
    def _normalize_date(self, date_str: str) -> str:
        """Normalize date string to YYYY-MM-DD format."""
        # Try different date formats
        formats = [
            r'(\d{1,2})[-/](\d{1,2})[-/](\d{4})',  # DD/MM/YYYY or DD-MM-YYYY
            r'(\d{4})[-/](\d{1,2})[-/](\d{1,2})',  # YYYY/MM/DD or YYYY-MM-DD
        ]
        
        for fmt in formats:
            match = re.search(fmt, date_str)
            if match:
                if len(match.group(3)) == 4:  # DD/MM/YYYY format
                    day, month, year = match.groups()
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                else:  # YYYY/MM/DD format
                    year, month, day = match.groups()
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        
        return date_str

    def _combine_and_validate(self, ner_entities: List[Entity], regex_entities: List[PayrollEntity]) -> List[PayrollEntity]:
        """
        Combine NER and regex results, removing duplicates and validating.

        Args:
            ner_entities: Entities from NER model
            regex_entities: Entities from regex patterns

        Returns:
            Combined and validated list of PayrollEntity objects
        """
        combined = []

        # Convert NER entities to PayrollEntity format
        for entity in ner_entities:
            payroll_entity = PayrollEntity(
                text=entity.text,
                label=entity.label,
                start=entity.start,
                end=entity.end,
                confidence=entity.confidence,
                field_type=self._get_field_type(entity.label),
                normalized_value=self._normalize_value(entity.text, entity.label)
            )
            combined.append(payroll_entity)

        # Add regex entities, avoiding duplicates
        for regex_entity in regex_entities:
            # Check for overlapping entities
            is_duplicate = False
            for existing in combined:
                if (abs(regex_entity.start - existing.start) < 10 and
                    regex_entity.label == existing.label):
                    # Prefer higher confidence entity
                    if regex_entity.confidence > existing.confidence:
                        combined.remove(existing)
                        combined.append(regex_entity)
                    is_duplicate = True
                    break

            if not is_duplicate:
                combined.append(regex_entity)

        # Validate entities
        validated = []
        for entity in combined:
            validation_status = self._validate_entity(entity)
            entity.validation_status = validation_status
            validated.append(entity)

        return validated

    def _validate_entity(self, entity: PayrollEntity) -> str:
        """
        Validate an extracted entity.

        Args:
            entity: PayrollEntity to validate

        Returns:
            Validation status: 'valid', 'invalid', 'needs_review'
        """
        if entity.field_type == "salary":
            # Validate salary amounts
            try:
                amount = float(entity.normalized_value)
                if amount < 0:
                    return "invalid"
                elif amount > 10000000:  # 1 crore limit
                    return "needs_review"
                elif amount < 1000:  # Very low salary
                    return "needs_review"
                return "valid"
            except (ValueError, TypeError):
                return "invalid"

        elif entity.field_type == "date":
            # Validate dates
            if entity.label == "MONTH":
                if re.match(r'\d{2}-\d{4}', entity.normalized_value):
                    return "valid"
            elif entity.label == "PAY_DATE":
                if re.match(r'\d{4}-\d{2}-\d{2}', entity.normalized_value):
                    return "valid"
            return "needs_review"

        elif entity.field_type == "personal":
            # Validate personal information
            if entity.label == "EMPLOYEE_ID":
                if len(entity.normalized_value) >= 3:
                    return "valid"
            elif entity.label == "EMPLOYEE_NAME":
                if len(entity.text.split()) >= 2:  # At least first and last name
                    return "valid"
            return "needs_review"

        return "valid"  # Default for other types

    def _structure_payroll_data(self, entities: List[PayrollEntity], original_text: str) -> Dict[str, Any]:
        """
        Structure extracted entities into a standardized payroll data format.

        Args:
            entities: List of extracted entities
            original_text: Original document text

        Returns:
            Structured payroll data dictionary
        """
        structured = {
            "document_type": "payslip",
            "employee_id": None,
            "employee_name": None,
            "organization": None,
            "month": None,
            "year": None,
            "pay_date": None,
            "gross_pay": None,
            "net_pay": None,
            "basic_salary": None,
            "hra": None,
            "tax": None,
            "pf": None,
            "esi": None,
            "professional_tax": None,
            "bonus": None,
            "currency": "INR",
            "validation_flags": []
        }

        # Map entities to structured fields
        for entity in entities:
            field_name = entity.label.lower()

            if entity.validation_status == "valid":
                if field_name == "employee_id":
                    structured["employee_id"] = entity.normalized_value
                elif field_name == "employee_name":
                    structured["employee_name"] = entity.normalized_value
                elif field_name == "organization":
                    structured["organization"] = entity.normalized_value
                elif field_name == "month":
                    month_year = entity.normalized_value.split('-')
                    if len(month_year) == 2:
                        structured["month"] = month_year[0]
                        structured["year"] = month_year[1]
                elif field_name == "pay_date":
                    structured["pay_date"] = entity.normalized_value
                elif field_name == "gross_pay":
                    structured["gross_pay"] = float(entity.normalized_value)
                elif field_name == "net_pay":
                    structured["net_pay"] = float(entity.normalized_value)
                elif field_name == "basic_salary":
                    structured["basic_salary"] = float(entity.normalized_value)
                elif field_name == "hra":
                    structured["hra"] = float(entity.normalized_value)
                elif field_name == "tax":
                    structured["tax"] = float(entity.normalized_value)
                elif field_name == "pf":
                    structured["pf"] = float(entity.normalized_value)
                elif field_name == "esi":
                    structured["esi"] = float(entity.normalized_value)
                elif field_name == "professional_tax":
                    structured["professional_tax"] = float(entity.normalized_value)
                elif field_name == "bonus":
                    structured["bonus"] = float(entity.normalized_value)

                # Set currency if found
                if entity.currency:
                    structured["currency"] = entity.currency

            elif entity.validation_status in ["invalid", "needs_review"]:
                structured["validation_flags"].append({
                    "field": field_name,
                    "value": entity.text,
                    "issue": entity.validation_status,
                    "confidence": entity.confidence
                })

        # Perform cross-field validation
        validation_issues = self._cross_validate_fields(structured)
        structured["validation_flags"].extend(validation_issues)

        return structured

    def _cross_validate_fields(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Perform cross-field validation on structured payroll data.

        Args:
            data: Structured payroll data

        Returns:
            List of validation issues
        """
        issues = []

        # Validate salary calculations
        if data.get("gross_pay") and data.get("net_pay"):
            gross = data["gross_pay"]
            net = data["net_pay"]

            # Calculate expected deductions
            expected_deductions = 0
            for field in ["tax", "pf", "esi", "professional_tax"]:
                if data.get(field):
                    expected_deductions += data[field]

            # Check if gross - deductions ≈ net (within 5% tolerance)
            expected_net = gross - expected_deductions
            if abs(expected_net - net) > (gross * 0.05):
                issues.append({
                    "field": "salary_calculation",
                    "value": f"Gross: {gross}, Net: {net}, Expected: {expected_net}",
                    "issue": "salary_mismatch",
                    "confidence": 0.9
                })

        # Validate date consistency
        if data.get("month") and data.get("year") and data.get("pay_date"):
            try:
                pay_date = datetime.strptime(data["pay_date"], "%Y-%m-%d")
                expected_month = data["month"]
                expected_year = data["year"]

                if (pay_date.month != int(expected_month) or
                    pay_date.year != int(expected_year)):
                    issues.append({
                        "field": "date_consistency",
                        "value": f"Pay date: {data['pay_date']}, Month: {expected_month}/{expected_year}",
                        "issue": "date_mismatch",
                        "confidence": 0.8
                    })
            except (ValueError, TypeError):
                pass

        return issues
