"""
Document classifier for payroll and HR documents.
Uses BGE embeddings with classification head for document type detection.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import pickle
import json

from sentence_transformers import SentenceTransformer
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report
from sklearn.model_selection import train_test_split

from ..utils.logger import get_logger
from ..config import DATA_DIR

logger = get_logger(__name__)

# Model paths
CLASSIFIER_MODEL_PATH = DATA_DIR / "models" / "payroll_classifier"
EMBEDDINGS_MODEL = "BAAI/bge-base-en-v1.5"

# Ensure model directory exists
CLASSIFIER_MODEL_PATH.mkdir(parents=True, exist_ok=True)


@dataclass
class ClassificationResult:
    """Result of document classification."""
    document_type: str
    confidence: float
    all_scores: Dict[str, float]
    processing_time: float


class PayrollDocumentClassifier:
    """
    Document classifier for payroll and HR documents using BGE embeddings.
    """
    
    # Document type definitions with keywords
    DOCUMENT_TYPES = {
        'payslip': [
            'salary', 'pay slip', 'payslip', 'gross pay', 'net pay', 'deductions',
            'basic salary', 'hra', 'provident fund', 'pf', 'tax deducted', 'tds',
            'employee id', 'pay period', 'earnings', 'allowances'
        ],
        'offer_letter': [
            'offer letter', 'job offer', 'appointment letter', 'employment offer',
            'position offered', 'joining date', 'annual ctc', 'compensation package',
            'terms and conditions', 'probation period', 'welcome to', 'pleased to offer'
        ],
        'bank_statement': [
            'bank statement', 'account statement', 'transaction history',
            'opening balance', 'closing balance', 'credit', 'debit', 'bank account',
            'statement period', 'account number', 'ifsc', 'branch'
        ],
        'tax_document': [
            'form 16', 'tax certificate', 'income tax', 'tds certificate',
            'annual tax', 'tax deduction', 'assessment year', 'financial year',
            'pan number', 'tax computation', 'rebate', 'tax liability'
        ],
        'leave_application': [
            'leave application', 'leave request', 'sick leave', 'casual leave',
            'annual leave', 'vacation request', 'time off', 'absence request',
            'leave balance', 'leave approval', 'leave from', 'leave to'
        ],
        'performance_review': [
            'performance review', 'appraisal', 'performance evaluation',
            'annual review', 'rating', 'goals', 'objectives', 'feedback',
            'performance rating', 'kpi', 'achievements', 'development plan'
        ],
        'contract': [
            'employment contract', 'service agreement', 'work contract',
            'terms of employment', 'contract period', 'renewal', 'termination',
            'confidentiality', 'non-disclosure', 'intellectual property'
        ],
        'other': [
            'memo', 'notice', 'policy', 'handbook', 'guidelines', 'procedure',
            'announcement', 'circular', 'communication', 'update'
        ]
    }
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize the document classifier.
        
        Args:
            model_path: Optional path to saved classifier model
        """
        self.model_path = model_path or str(CLASSIFIER_MODEL_PATH)
        self.embeddings_model = None
        self.classifier = None
        self.label_encoder = None
        
        # Load embeddings model
        self._load_embeddings_model()
        
        # Try to load trained classifier
        if Path(self.model_path).exists():
            self._load_classifier()
        else:
            logger.info("No trained classifier found. Will need to train first.")
    
    def _load_embeddings_model(self):
        """Load the BGE embeddings model."""
        try:
            logger.info(f"Loading embeddings model: {EMBEDDINGS_MODEL}")
            self.embeddings_model = SentenceTransformer(EMBEDDINGS_MODEL)
            logger.info("Embeddings model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load embeddings model: {e}")
            raise
    
    def _load_classifier(self):
        """Load the trained classifier from disk."""
        try:
            classifier_path = Path(self.model_path) / "classifier.pkl"
            encoder_path = Path(self.model_path) / "label_encoder.pkl"
            
            if classifier_path.exists() and encoder_path.exists():
                with open(classifier_path, 'rb') as f:
                    self.classifier = pickle.load(f)
                
                with open(encoder_path, 'rb') as f:
                    self.label_encoder = pickle.load(f)
                
                logger.info(f"Classifier loaded from: {self.model_path}")
            else:
                logger.warning("Classifier files not found")
        except Exception as e:
            logger.error(f"Failed to load classifier: {e}")
            raise
    
    def _save_classifier(self):
        """Save the trained classifier to disk."""
        try:
            Path(self.model_path).mkdir(parents=True, exist_ok=True)
            
            classifier_path = Path(self.model_path) / "classifier.pkl"
            encoder_path = Path(self.model_path) / "label_encoder.pkl"
            
            with open(classifier_path, 'wb') as f:
                pickle.dump(self.classifier, f)
            
            with open(encoder_path, 'wb') as f:
                pickle.dump(self.label_encoder, f)
            
            # Save metadata
            metadata = {
                "model_type": "LogisticRegression",
                "embeddings_model": EMBEDDINGS_MODEL,
                "document_types": list(self.DOCUMENT_TYPES.keys()),
                "created_at": str(Path(classifier_path).stat().st_mtime)
            }
            
            with open(Path(self.model_path) / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Classifier saved to: {self.model_path}")
        except Exception as e:
            logger.error(f"Failed to save classifier: {e}")
            raise
    
    def classify_document(self, text: str) -> ClassificationResult:
        """
        Classify a document based on its text content.
        
        Args:
            text: Document text content
            
        Returns:
            ClassificationResult with predicted document type and confidence
        """
        import time
        start_time = time.time()
        
        if not self.classifier or not self.embeddings_model:
            raise ValueError("Classifier not trained or loaded")
        
        try:
            # Generate embeddings for the text
            embeddings = self.embeddings_model.encode([text])
            
            # Get prediction probabilities
            probabilities = self.classifier.predict_proba(embeddings)[0]
            
            # Get predicted class
            predicted_class_idx = np.argmax(probabilities)
            predicted_class = self.label_encoder[predicted_class_idx]
            confidence = probabilities[predicted_class_idx]
            
            # Create scores dictionary
            all_scores = {}
            for i, label in enumerate(self.label_encoder):
                all_scores[label] = float(probabilities[i])
            
            processing_time = time.time() - start_time
            
            logger.info(f"Classified document as '{predicted_class}' with confidence {confidence:.3f}")
            
            return ClassificationResult(
                document_type=predicted_class,
                confidence=float(confidence),
                all_scores=all_scores,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"Classification failed: {e}")
            # Return default classification
            return ClassificationResult(
                document_type="other",
                confidence=0.0,
                all_scores={"other": 1.0},
                processing_time=time.time() - start_time
            )
    
    def train_classifier(self, training_data: Optional[List[Tuple[str, str]]] = None):
        """
        Train the document classifier.
        
        Args:
            training_data: Optional list of (text, label) tuples. If None, generates synthetic data.
        """
        if training_data is None:
            logger.info("No training data provided. Generating synthetic training data...")
            training_data = self._generate_synthetic_training_data()
        
        logger.info(f"Training classifier with {len(training_data)} samples")
        
        # Prepare data
        texts, labels = zip(*training_data)
        
        # Generate embeddings
        logger.info("Generating embeddings for training data...")
        embeddings = self.embeddings_model.encode(list(texts))
        
        # Prepare labels
        unique_labels = sorted(list(set(labels)))
        self.label_encoder = unique_labels
        label_indices = [unique_labels.index(label) for label in labels]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            embeddings, label_indices, test_size=0.2, random_state=42, stratify=label_indices
        )
        
        # Train classifier
        logger.info("Training logistic regression classifier...")
        self.classifier = LogisticRegression(
            max_iter=1000,
            random_state=42,
            class_weight='balanced'
        )
        self.classifier.fit(X_train, y_train)
        
        # Evaluate
        y_pred = self.classifier.predict(X_test)
        report = classification_report(y_test, y_pred, target_names=unique_labels)
        logger.info(f"Classification report:\n{report}")
        
        # Save the trained model
        self._save_classifier()
        
        logger.info("Classifier training completed successfully")
    
    def _generate_synthetic_training_data(self) -> List[Tuple[str, str]]:
        """
        Generate synthetic training data based on document type keywords.
        
        Returns:
            List of (text, label) tuples
        """
        training_data = []
        
        for doc_type, keywords in self.DOCUMENT_TYPES.items():
            # Generate multiple samples per document type
            for i in range(20):  # 20 samples per type
                # Create synthetic text by combining keywords
                sample_keywords = np.random.choice(keywords, size=min(5, len(keywords)), replace=False)
                
                if doc_type == 'payslip':
                    text = f"Employee payslip for {sample_keywords[0]}. Gross salary: 50000. Net pay: 42000. {' '.join(sample_keywords[1:])}"
                elif doc_type == 'offer_letter':
                    text = f"We are pleased to offer you the position. {' '.join(sample_keywords)}. Your annual CTC will be 600000."
                elif doc_type == 'bank_statement':
                    text = f"Bank account statement. {' '.join(sample_keywords)}. Opening balance: 25000. Closing balance: 30000."
                elif doc_type == 'tax_document':
                    text = f"Income tax certificate. {' '.join(sample_keywords)}. Tax deducted at source: 15000."
                elif doc_type == 'leave_application':
                    text = f"Application for leave. {' '.join(sample_keywords)}. Requesting 3 days leave from next week."
                elif doc_type == 'performance_review':
                    text = f"Annual performance evaluation. {' '.join(sample_keywords)}. Overall rating: Exceeds expectations."
                elif doc_type == 'contract':
                    text = f"Employment agreement. {' '.join(sample_keywords)}. Contract period: 2 years."
                else:  # other
                    text = f"Company document. {' '.join(sample_keywords)}. Please review and acknowledge."
                
                training_data.append((text, doc_type))
        
        logger.info(f"Generated {len(training_data)} synthetic training samples")
        return training_data
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the classifier model."""
        return {
            "model_path": self.model_path,
            "embeddings_model": EMBEDDINGS_MODEL,
            "document_types": list(self.DOCUMENT_TYPES.keys()),
            "is_trained": self.classifier is not None,
            "model_type": "LogisticRegression with BGE embeddings"
        }
