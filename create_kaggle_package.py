#!/usr/bin/env python3
"""
Create a zip package for Kaggle NER training
"""

import zipfile
import os
from pathlib import Path

def create_kaggle_package():
    """Create zip package with all necessary files."""
    
    files_to_include = [
        "kaggle_ner_final.py",
        "kaggle_ner_inference.py", 
        "kaggle_requirements.txt",
        "KAGGLE_INSTRUCTIONS.md"
    ]
    
    zip_name = "kaggle_ner_package.zip"
    
    print("📦 Creating Kaggle NER package...")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_name in files_to_include:
            if os.path.exists(file_name):
                zipf.write(file_name)
                print(f"✅ Added: {file_name}")
            else:
                print(f"❌ Missing: {file_name}")
    
    print(f"\n🎉 Package created: {zip_name}")
    print(f"📁 Size: {os.path.getsize(zip_name) / 1024:.1f} KB")
    
    print("\n📋 NEXT STEPS:")
    print("1. Extract the zip file")
    print("2. Read KAGGLE_INSTRUCTIONS.md")
    print("3. Copy kaggle_ner_final.py to Kaggle")
    print("4. Run the training!")

if __name__ == "__main__":
    create_kaggle_package()
