# Model files (too large for GitHub)
models/
data/models/
data/models_cache/
*.safetensors
*.bin
*.pt
*.pth
*.onnx
*.pkl
*.pickle
*.h5
*.hdf5
*.model
*.torch

# Cache directories
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
.env
venv/
ENV/
env.bak/
venv.bak/

# Logs
logs/
*.log

# Training outputs
checkpoints/
runs/
wandb/

# Data files
data/training/*.jsonl
data/cache/
data/db/
data/processed/
data/raw/
data/raw_files/
data/metrics/

# Database files
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm
data/db/
admin_dashboard/data/*.db
admin_dashboard/data/*.sqlite
admin_dashboard/data/*.sqlite3

# Admin dashboard data
admin_dashboard/data/
admin_dashboard_ui/data/

# Zip files
*.zip
data/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
