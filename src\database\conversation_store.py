"""
Conversation storage and retrieval functionality.
"""
import time
from typing import List, Dict, Any

from ..utils.logger import get_logger
from .user_db import ConversationModel
from ..config import MAX_HISTORY_MESSAGES

logger = get_logger(__name__)

class ConversationStore:
    """Manages conversation storage and retrieval."""
    
    def __init__(self):
        """Initialize the conversation store."""
        self.model = ConversationModel()

    
    def save_conversation(self, chat_id: str, user_query: str, assistant_response: str, 
                         language: str, device_id: str, 
                         query_start_time: float = None, 
                         response_end_time: float = None, 
                         intent: str = None) -> int:
        """
        Save a conversation entry to the database.
        Args:
            chat_id: Unique chat identifier for the chat session
            user_query: The user's query
            assistant_response: The assistant's response
            language: The detected language code
            device_id: Unique identifier for the user/device
            query_start_time: Timestamp when query was received (defaults to current time)
            response_end_time: Timestamp when response was generated (defaults to current time)
            intent: Classified intent (optional)
        Returns:
            The ID of the saved conversation entry
        """
        # Use current time if timestamps not provided
        if query_start_time is None:
            query_start_time = time.time()
        if response_end_time is None:
            response_end_time = time.time()
        try:
            conversation_id = self.model.save_conversation(
                chat_id=chat_id,
                device_id=device_id,
                user_query=user_query,
                assistant_response=assistant_response,
                language=language,
                query_timestamp=query_start_time,
                response_timestamp=response_end_time,
                intent=intent
            )
            logger.info(f"Saved conversation for device {device_id} with ID {conversation_id}")
            return conversation_id
        except Exception as e:
            logger.error(f"Error saving conversation: {e}")
            return None

    def get_conversation_history(self, device_id: str, limit: int = MAX_HISTORY_MESSAGES) -> List[Dict[str, Any]]:
        """
        Get conversation history for a device.
        
        Args:
            device_id: Unique identifier for the user/device
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of conversation entries
        """

        
        try:
            conversations = self.model.get_conversations(device_id, limit)
            # Format the conversations for easier consumption
            formatted_conversations = []
            for conv in conversations:
                formatted_conversations.append({
                    "user_query": conv["user_query"],
                    "assistant_response": conv["assistant_response"],
                    "language": conv["language"],
                    "timestamp": conv["query_timestamp"]
                })
            
            # 
            
            logger.info(f"Retrieved {len(formatted_conversations)} conversations for device {device_id}")
            return formatted_conversations
            
        except Exception as e:
            logger.error(f"Error retrieving conversation history: {e}")
            return []

    def get_chat_messages(self, chat_id: str, offset: int = 0, limit: int = 20):
        """
        Get messages for a specific chat_id with pagination.
        Returns only user_query, assistant_response, language, and timestamp for each message.
        """
        try:
            messages = self.model.get_chat_messages(chat_id, offset, limit)
            formatted_messages = []
            for msg in messages:
                formatted_messages.append({
                    "user_query": msg.get("user_query", ""),
                    "assistant_response": msg.get("assistant_response", ""),
                    "language": msg.get("language", ""),
                    "timestamp": msg.get("query_timestamp", msg.get("timestamp", ""))
                })
            return formatted_messages
        except Exception as e:
            logger.error(f"Error retrieving chat messages: {e}")
            return []

    def get_all_conversations(self, device_id=None, start_date=None, end_date=None, limit=100, offset=0):
        """
        Fetch all conversations with optional filtering by user/session, date range, and pagination.
        Args:
            device_id: filter by user/session (optional)
            start_date: filter by start date (ISO string, optional)
            end_date: filter by end date (ISO string, optional)
            limit: max results
            offset: for pagination
        Returns:
            List of conversation dicts
        """
        try:
            conversations = self.model.get_conversations(device_id=device_id, start_date=start_date, end_date=end_date, limit=limit, offset=offset)
            logger.info(f"Retrieved {len(conversations)} conversations (filters: device_id={device_id}, start={start_date}, end={end_date})")
            return conversations
        except Exception as e:
            logger.error(f"Error retrieving all conversations: {e}")
            return []
