"""
Main payroll document processor that orchestrates all components.
Handles end-to-end processing of payroll documents from upload to storage.
"""

import logging
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import asyncio

from .file_processor import FileProcessor
from .payroll_classifier import PayrollDocumentClassifier
from .issue_detector import PayrollIssueDetector
from ..ner.payroll_entity_extractor import PayrollEntityExtractor
from ..database.payroll_db import PayrollDatabase, PayrollDocument, ParsedField, DetectedIssue as DBDetectedIssue
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ProcessingResult:
    """Result of payroll document processing."""
    success: bool
    document_id: Optional[int] = None
    document_type: str = "unknown"
    structured_data: Optional[Dict[str, Any]] = None
    extracted_fields: List[Dict[str, Any]] = None
    detected_issues: List[Dict[str, Any]] = None
    processing_time: float = 0.0
    error_message: Optional[str] = None
    confidence_score: float = 0.0
    auto_resolved: bool = False


class PayrollDocumentProcessor:
    """
    Main processor for payroll documents.
    Orchestrates classification, entity extraction, issue detection, and storage.
    """
    
    def __init__(self, 
                 db_path: Optional[str] = None,
                 ner_model_path: Optional[str] = None,
                 classifier_model_path: Optional[str] = None):
        """
        Initialize the payroll processor.
        
        Args:
            db_path: Optional database path
            ner_model_path: Optional NER model path
            classifier_model_path: Optional classifier model path
        """
        self.db = PayrollDatabase(db_path)
        self.file_processor = FileProcessor()
        self.classifier = PayrollDocumentClassifier(classifier_model_path)
        self.entity_extractor = PayrollEntityExtractor(ner_model_path)
        self.issue_detector = PayrollIssueDetector()
        
        logger.info("PayrollDocumentProcessor initialized successfully")
    
    def process_document(self, file_path: Path, employee_id: Optional[str] = None) -> ProcessingResult:
        """
        Process a single payroll document end-to-end.
        
        Args:
            file_path: Path to the document file
            employee_id: Optional employee ID for context
            
        Returns:
            ProcessingResult with all extracted information
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing document: {file_path.name}")
            
            # Step 1: Extract text from document
            extracted_doc = self.file_processor.process_file(file_path)
            if extracted_doc.get("file_type") == "error":
                return ProcessingResult(
                    success=False,
                    error_message=f"File processing failed: {extracted_doc.get('content', 'Unknown error')}",
                    processing_time=time.time() - start_time
                )
            
            text_content = extracted_doc["content"]
            
            # Step 2: Classify document type
            classification_result = self.classifier.classify_document(text_content)
            document_type = classification_result.document_type
            
            logger.info(f"Document classified as: {document_type} (confidence: {classification_result.confidence:.3f})")
            
            # Step 3: Extract entities (only for payroll-related documents)
            if document_type in ['payslip', 'offer_letter', 'tax_document']:
                extraction_result = self.entity_extractor.extract_payroll_entities(text_content)
                structured_data = extraction_result["structured_data"]
                entities = extraction_result["entities"]
            else:
                # For non-payroll documents, use basic entity extraction
                basic_extraction = self.entity_extractor.extract_entities(text_content)
                structured_data = {"document_type": document_type}
                entities = basic_extraction.entities
            
            # Step 4: Detect issues
            detected_issues = []
            if document_type == 'payslip' and structured_data:
                detected_issues = self.issue_detector.detect_document_issues(structured_data)
            
            # Step 4.5: Auto-resolve if no issues detected
            auto_resolved = False
            if document_type == 'payslip' and not detected_issues:
                auto_resolved = True
                logger.info(f"No discrepancies found in document {file_path.name}. Auto-resolving.")
            
            # Step 5: Calculate file hash for deduplication
            file_hash = self._calculate_file_hash(file_path)
            
            # Step 6: Store in database
            document_id = self._store_document_data(
                file_path=file_path,
                file_hash=file_hash,
                document_type=document_type,
                structured_data=structured_data,
                entities=entities,
                detected_issues=detected_issues,
                employee_id=employee_id
            )
            
            # Step 7: Calculate overall confidence score
            confidence_score = self._calculate_confidence_score(
                classification_result.confidence,
                entities,
                detected_issues
            )
            
            # Update document status - auto-resolve if no issues
            if auto_resolved:
                self.db.update_document_status(document_id, "resolved", confidence_score)
                self.db.track_auto_resolution(document_id)
                logger.info(f"Document {document_id} auto-resolved due to no detected issues")
            else:
            self.db.update_document_status(document_id, "processed", confidence_score)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Document processed successfully in {processing_time:.3f}s (ID: {document_id})")
            
            return ProcessingResult(
                success=True,
                document_id=document_id,
                document_type=document_type,
                structured_data=structured_data,
                extracted_fields=[asdict(entity) for entity in entities] if hasattr(entities[0], '__dict__') else entities,
                detected_issues=[asdict(issue) for issue in detected_issues] if detected_issues and hasattr(detected_issues[0], '__dict__') else detected_issues,
                processing_time=processing_time,
                confidence_score=confidence_score,
                auto_resolved=auto_resolved
            )
            
        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    async def process_document_async(self, file_path: Path, employee_id: Optional[str] = None) -> ProcessingResult:
        """
        Async version of process_document for better performance.
        
        Args:
            file_path: Path to the document file
            employee_id: Optional employee ID for context
            
        Returns:
            ProcessingResult with all extracted information
        """
        start_time = time.time()
        
        try:
            logger.info(f"Processing document: {file_path.name}")
            
            # Step 1: Extract text from document
            extracted_doc = await asyncio.get_event_loop().run_in_executor(
                None, self.file_processor.process_file, file_path
            )
            if extracted_doc.get("file_type") == "error":
                return ProcessingResult(
                    success=False,
                    error_message=f"File processing failed: {extracted_doc.get('content', 'Unknown error')}",
                    processing_time=time.time() - start_time
                )
            
            text_content = extracted_doc["content"]
            
            # Step 2: Classify document type
            classification_result = await asyncio.get_event_loop().run_in_executor(
                None, self.classifier.classify_document, text_content
            )
            document_type = classification_result.document_type
            
            logger.info(f"Document classified as: {document_type} (confidence: {classification_result.confidence:.3f})")
            
            # Step 3: Extract entities (only for payroll-related documents)
            if document_type in ['payslip', 'offer_letter', 'tax_document']:
                extraction_result = await asyncio.get_event_loop().run_in_executor(
                    None, self.entity_extractor.extract_payroll_entities, text_content
                )
                structured_data = extraction_result["structured_data"]
                entities = extraction_result["entities"]
            else:
                # For non-payroll documents, use basic entity extraction
                basic_extraction = await asyncio.get_event_loop().run_in_executor(
                    None, self.entity_extractor.extract_entities, text_content
                )
                structured_data = {"document_type": document_type}
                entities = basic_extraction.entities
            
            # Step 4: Detect issues
            detected_issues = []
            if document_type == 'payslip' and structured_data:
                detected_issues = await self.issue_detector.detect_document_issues_async(structured_data)
            
            # Step 4.5: Auto-resolve if no issues detected
            auto_resolved = False
            if document_type == 'payslip' and not detected_issues:
                auto_resolved = True
                logger.info(f"No discrepancies found in document {file_path.name}. Auto-resolving.")
            
            # Step 5: Calculate file hash for deduplication
            file_hash = await asyncio.get_event_loop().run_in_executor(
                None, self._calculate_file_hash, file_path
            )
            
            # Step 6: Store in database
            document_id = await self._store_document_data_async(
                file_path=file_path,
                file_hash=file_hash,
                document_type=document_type,
                structured_data=structured_data,
                entities=entities,
                detected_issues=detected_issues,
                employee_id=employee_id
            )
            
            # Step 7: Calculate overall confidence score
            confidence_score = await asyncio.get_event_loop().run_in_executor(
                None, self._calculate_confidence_score,
                classification_result.confidence,
                entities,
                detected_issues
            )
            
            # Update document status - auto-resolve if no issues
            if auto_resolved:
                await self.db.update_document_status_async(document_id, "resolved", confidence_score)
                await self.db.track_auto_resolution_async(document_id)
                logger.info(f"Document {document_id} auto-resolved due to no detected issues")
            else:
                await self.db.update_document_status_async(document_id, "processed", confidence_score)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Document processed successfully in {processing_time:.3f}s (ID: {document_id})")
            
            return ProcessingResult(
                success=True,
                document_id=document_id,
                document_type=document_type,
                structured_data=structured_data,
                extracted_fields=[asdict(entity) for entity in entities] if hasattr(entities[0], '__dict__') else entities,
                detected_issues=[asdict(issue) for issue in detected_issues] if detected_issues and hasattr(detected_issues[0], '__dict__') else detected_issues,
                processing_time=processing_time,
                confidence_score=confidence_score,
                auto_resolved=auto_resolved
            )
            
        except Exception as e:
            logger.error(f"Document processing failed: {e}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def process_batch(self, file_paths: List[Path], employee_ids: Optional[List[str]] = None) -> List[ProcessingResult]:
        """
        Process multiple documents in batch.
        
        Args:
            file_paths: List of file paths to process
            employee_ids: Optional list of employee IDs (must match file_paths length)
            
        Returns:
            List of ProcessingResult objects
        """
        if employee_ids and len(employee_ids) != len(file_paths):
            raise ValueError("employee_ids length must match file_paths length")
        
        results = []
        
        for i, file_path in enumerate(file_paths):
            employee_id = employee_ids[i] if employee_ids else None
            result = self.process_document(file_path, employee_id)
            results.append(result)
        
        successful_count = sum(1 for r in results if r.success)
        logger.info(f"Batch processing completed: {successful_count}/{len(file_paths)} successful")
        
        return results
    
    def analyze_complaint(self, complaint_text: str, employee_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze an HR complaint and detect issues.
        
        Args:
            complaint_text: Text of the complaint
            employee_id: Optional employee ID for context
            
        Returns:
            Dictionary with analysis results
        """
        start_time = time.time()
        
        try:
            # Get recent documents for context if employee_id provided
            document_data = None
            if employee_id:
                recent_docs = self.db.get_documents_by_employee(employee_id, limit=1)
                if recent_docs:
                    doc_details = self.db.get_document_with_details(recent_docs[0].id)
                    if doc_details:
                        # Convert fields to structured data format
                        document_data = {}
                        for field in doc_details["fields"]:
                            document_data[field["field_name"]] = field["field_value"]
            
            # Classify complaint
            detected_issues = self.issue_detector.classify_complaint(complaint_text, document_data)
            
            # Check if auto-resolved (no issues detected)
            auto_resolved = len(detected_issues) == 0
            
            # Get statistics
            issue_stats = self.issue_detector.get_issue_statistics(detected_issues)
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "detected_issues": [asdict(issue) for issue in detected_issues],
                "statistics": issue_stats,
                "processing_time": processing_time,
                "employee_id": employee_id,
                "has_context": document_data is not None,
                "auto_resolved": auto_resolved
            }
            
        except Exception as e:
            logger.error(f"Complaint analysis failed: {e}")
            return {
                "success": False,
                "error_message": str(e),
                "processing_time": time.time() - start_time,
                "auto_resolved": False
            }
    
    async def analyze_complaint_async(self, complaint_text: str, employee_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Async version of analyze_complaint for better performance.
        
        Args:
            complaint_text: Text of the complaint
            employee_id: Optional employee ID for context
            
        Returns:
            Dictionary with analysis results
        """
        start_time = time.time()
        
        try:
            # Get recent documents for context if employee_id provided
            document_data = None
            if employee_id:
                recent_docs = await self.db.get_documents_by_employee_async(employee_id, limit=1)
                if recent_docs:
                    doc_details = await asyncio.get_event_loop().run_in_executor(
                        None, self.db.get_document_with_details, recent_docs[0].id
                    )
                    if doc_details:
                        # Convert fields to structured data format
                        document_data = {}
                        for field in doc_details["fields"]:
                            document_data[field["field_name"]] = field["field_value"]
            
            # Classify complaint
            detected_issues = await self.issue_detector.classify_complaint_async(complaint_text, document_data)
            
            # Check if auto-resolved (no issues detected)
            auto_resolved = len(detected_issues) == 0
            
            # Get statistics
            issue_stats = await asyncio.get_event_loop().run_in_executor(
                None, self.issue_detector.get_issue_statistics, detected_issues
            )
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "detected_issues": [asdict(issue) for issue in detected_issues],
                "statistics": issue_stats,
                "processing_time": processing_time,
                "employee_id": employee_id,
                "has_context": document_data is not None,
                "auto_resolved": auto_resolved
            }
            
        except Exception as e:
            logger.error(f"Complaint analysis failed: {e}")
            return {
                "success": False,
                "error_message": str(e),
                "processing_time": time.time() - start_time,
                "auto_resolved": False
            }
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file for deduplication."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _store_document_data(self, 
                           file_path: Path,
                           file_hash: str,
                           document_type: str,
                           structured_data: Dict[str, Any],
                           entities: List[Any],
                           detected_issues: List[Any],
                           employee_id: Optional[str] = None) -> int:
        """Store document and extracted data in database."""
        
        # Create document record
        document = PayrollDocument(
            employee_id=employee_id or structured_data.get("employee_id"),
            employee_name=structured_data.get("employee_name"),
            organization=structured_data.get("organization"),
            month=structured_data.get("month"),
            year=structured_data.get("year"),
            document_type=document_type,
            file_path=str(file_path),
            file_hash=file_hash,
            processing_status="processing"
        )
        
        # Insert document
        document_id = self.db.insert_document(document)
        
        # Store parsed fields
        parsed_fields = []
        for entity in entities:
            if hasattr(entity, 'label'):  # PayrollEntity or Entity object
                field = ParsedField(
                    document_id=document_id,
                    field_name=entity.label,
                    field_value=getattr(entity, 'normalized_value', entity.text),
                    confidence=entity.confidence,
                    extraction_method="ner",
                    flagged=getattr(entity, 'validation_status', 'valid') != 'valid'
                )
                parsed_fields.append(field)
        
        if parsed_fields:
            self.db.insert_parsed_fields(document_id, parsed_fields)
        
        # Store detected issues
        db_issues = []
        for issue in detected_issues:
            if hasattr(issue, 'issue_type'):  # DetectedIssue object
                db_issue = DBDetectedIssue(
                    document_id=document_id,
                    issue_type=issue.issue_type,
                    description=issue.description,
                    confidence=issue.confidence,
                    status="open"
                )
                db_issues.append(db_issue)
        
        if db_issues:
            self.db.insert_detected_issues(document_id, db_issues)
        
        return document_id
    
    async def _store_document_data_async(self, 
                                        file_path: Path,
                                        file_hash: str,
                                        document_type: str,
                                        structured_data: Dict[str, Any],
                                        entities: List[Any],
                                        detected_issues: List[Any],
                                        employee_id: Optional[str] = None) -> int:
        """Async version of _store_document_data."""
        # Create document record
        document = PayrollDocument(
            employee_id=employee_id,
            document_type=document_type,
            file_path=str(file_path),
            file_hash=file_hash,
            processing_status="pending"
        )
        
        # Insert document
        document_id = await self.db.insert_document_async(document)
        
        # Store parsed fields and issues in parallel
        if entities:
            # Convert entities to ParsedField objects
            parsed_fields = []
            for entity in entities:
                if hasattr(entity, 'label') and hasattr(entity, 'text'):
                    field = ParsedField(
                        document_id=document_id,
                        field_name=entity.label,
                        field_value=entity.text,
                        confidence=getattr(entity, 'confidence', 0.0),
                        extraction_method="ner"
                    )
                    parsed_fields.append(field)
            
            # Store fields
            if parsed_fields:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.db.insert_parsed_fields, document_id, parsed_fields
                )
        
        # Store detected issues
        if detected_issues:
            await asyncio.get_event_loop().run_in_executor(
                None, self.db.insert_detected_issues, document_id, detected_issues
            )
        
        return document_id
    
    def _calculate_confidence_score(self, 
                                  classification_confidence: float,
                                  entities: List[Any],
                                  detected_issues: List[Any]) -> float:
        """Calculate overall confidence score for the document processing."""
        
        # Base score from classification
        score = classification_confidence * 0.4
        
        # Entity extraction confidence
        if entities:
            entity_confidences = [getattr(e, 'confidence', 0.5) for e in entities]
            avg_entity_confidence = sum(entity_confidences) / len(entity_confidences)
            score += avg_entity_confidence * 0.4
        else:
            score += 0.2  # Partial score if no entities
        
        # Issue detection impact (fewer high-confidence issues = better)
        if detected_issues:
            high_conf_issues = [i for i in detected_issues if getattr(i, 'confidence', 0) > 0.7]
            issue_penalty = min(len(high_conf_issues) * 0.05, 0.2)
            score -= issue_penalty
        
        # Validation status bonus
        if entities:
            valid_entities = [e for e in entities if getattr(e, 'validation_status', 'valid') == 'valid']
            validation_bonus = (len(valid_entities) / len(entities)) * 0.2
            score += validation_bonus
        
        return max(0.0, min(1.0, score))  # Clamp between 0 and 1
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get overall processing statistics."""
        return self.db.get_statistics()
    
    def get_document_details(self, document_id: int) -> Optional[Dict[str, Any]]:
        """Get complete details for a document."""
        return self.db.get_document_with_details(document_id)
