#!/usr/bin/env python3
"""
Kaggle-optimized NER training script for Entity Extractor using SpanMarker.
This script is specifically designed to work in Kaggle kernels with GPU acceleration.
"""

import os
import sys
import json
import logging
import time
import gc
import warnings
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"

# Configure logging for Kaggle
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required packages for Kaggle environment."""
    try:
        import subprocess
        packages = [
            "span-marker",
            "transformers>=4.21.0",
            "torch>=1.12.0",
            "datasets",
            "scikit-learn",
            "psutil"
        ]
        
        for package in packages:
            try:
                __import__(package.split('>=')[0].replace('-', '_'))
                logger.info(f"✓ {package} already installed")
            except ImportError:
                logger.info(f"Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package, "--quiet"])
                logger.info(f"✓ {package} installed successfully")
    except Exception as e:
        logger.error(f"Failed to install requirements: {e}")
        raise

# Install requirements first
install_requirements()

# Now import the required libraries
import torch
import numpy as np
from datasets import Dataset
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
from span_marker import SpanMarkerModel, SpanMarkerTrainer
from transformers import TrainingArguments, EarlyStoppingCallback
import psutil

def detect_kaggle_environment() -> bool:
    """Detect if running in Kaggle environment."""
    kaggle_indicators = [
        os.path.exists('/kaggle'),
        'KAGGLE_KERNEL_RUN_TYPE' in os.environ,
        'KAGGLE_URL_BASE' in os.environ,
        '/kaggle' in os.getcwd()
    ]
    return any(kaggle_indicators)

def get_kaggle_paths():
    """Get appropriate paths for Kaggle environment."""
    if detect_kaggle_environment():
        # Kaggle paths
        input_dir = Path('/kaggle/input')
        working_dir = Path('/kaggle/working')
        
        # Look for training data in input
        training_data_path = None
        for path in input_dir.rglob('entity_training_data.jsonl'):
            training_data_path = path
            break
        
        if not training_data_path:
            # Create sample data if not found
            training_data_path = working_dir / 'entity_training_data.jsonl'
            create_sample_training_data(training_data_path)
        
        output_path = working_dir / 'ner_model'
        
    else:
        # Local paths
        training_data_path = Path('data/training/entity_training_data.jsonl')
        output_path = Path('data/models/ner_model')
    
    return training_data_path, output_path

def create_sample_training_data(file_path: Path):
    """Create sample training data for testing."""
    logger.info("Creating sample training data...")
    
    sample_data = [
        {"text": "John Smith works at Microsoft in Seattle.", "entities": [[0, 10, "PERSON"], [20, 29, "ORG"], [33, 40, "LOC"]]},
        {"text": "Apple Inc. is located in Cupertino, California.", "entities": [[0, 10, "ORG"], [25, 48, "LOC"]]},
        {"text": "Sarah Johnson joined Google last year.", "entities": [[0, 13, "PERSON"], [21, 27, "ORG"]]},
        {"text": "The meeting is scheduled in New York.", "entities": [[28, 36, "LOC"]]},
        {"text": "Amazon headquarters are in Seattle, Washington.", "entities": [[0, 6, "ORG"], [27, 47, "LOC"]]},
        {"text": "Mark Davis will visit Boston next month.", "entities": [[0, 10, "PERSON"], [22, 28, "LOC"]]},
        {"text": "Tesla factory is located in Austin, Texas.", "entities": [[0, 5, "ORG"], [28, 41, "LOC"]]},
        {"text": "Lisa Chen works for Facebook in Menlo Park.", "entities": [[0, 9, "PERSON"], [20, 28, "ORG"], [32, 42, "LOC"]]},
        {"text": "Netflix office is in Los Angeles.", "entities": [[0, 7, "ORG"], [21, 32, "LOC"]]},
        {"text": "David Wilson joined IBM last quarter.", "entities": [[0, 12, "PERSON"], [20, 23, "ORG"]]},
    ]
    
    # Duplicate to have enough samples
    extended_data = sample_data * 10
    
    file_path.parent.mkdir(parents=True, exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in extended_data:
            f.write(json.dumps(item) + '\n')
    
    logger.info(f"Created {len(extended_data)} sample training records")

def get_gpu_info():
    """Get GPU information for optimization."""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        logger.info(f"GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        return gpu_memory
    else:
        logger.warning("CUDA not available, using CPU")
        return 0

def get_optimal_batch_size(gpu_memory_gb: float) -> int:
    """Get optimal batch size for Kaggle environment."""
    if gpu_memory_gb >= 15:  # P100 or better
        return 4
    elif gpu_memory_gb >= 8:   # T4
        return 2
    else:
        return 1

def load_and_convert_data(file_path: Path, max_samples: int = 1000) -> List[Dict[str, Any]]:
    """Load and convert training data to SpanMarker format with proper tokenization."""
    logger.info(f"Loading training data from {file_path}")

    training_data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if max_samples and len(training_data) >= max_samples:
                break

            try:
                data = json.loads(line.strip())
                text = data["text"]
                entities = data["entities"]

                # Use whitespace tokenization for simplicity
                tokens = text.split()
                ner_tags = ["O"] * len(tokens)

                # Map character-based entities to token-based BIO tags
                for start_char, end_char, label in entities:
                    entity_text = text[start_char:end_char]

                    # Find which tokens overlap with this entity
                    current_pos = 0
                    entity_tokens_found = False

                    for i, token in enumerate(tokens):
                        # Find token position in original text
                        token_start = text.find(token, current_pos)
                        if token_start == -1:
                            continue
                        token_end = token_start + len(token)

                        # Check if token overlaps with entity
                        if (token_start < end_char and token_end > start_char):
                            if not entity_tokens_found:
                                ner_tags[i] = f"B-{label}"
                                entity_tokens_found = True
                            else:
                                ner_tags[i] = f"I-{label}"

                        current_pos = token_end

                if tokens and ner_tags and len(tokens) == len(ner_tags):
                    training_data.append({
                        "tokens": tokens,
                        "ner_tags": ner_tags
                    })

            except Exception as e:
                logger.warning(f"Error processing line {line_num}: {e}")
                continue

    logger.info(f"Loaded {len(training_data)} training samples")
    return training_data

def validate_data_distribution(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Ensure each label class has at least 2 samples for proper splitting."""
    from collections import Counter

    # Count label occurrences
    label_counts = Counter()
    for sample in data:
        for tag in sample["ner_tags"]:
            if tag != "O":
                label_counts[tag] += 1

    # Remove labels with insufficient samples
    insufficient_labels = [label for label, count in label_counts.items() if count < 2]

    if insufficient_labels:
        logger.warning(f"Removing labels with <2 samples: {insufficient_labels}")

        filtered_data = []
        for sample in data:
            filtered_tags = []
            for tag in sample["ner_tags"]:
                if tag in insufficient_labels:
                    filtered_tags.append("O")
                else:
                    filtered_tags.append(tag)

            filtered_data.append({
                "tokens": sample["tokens"],
                "ner_tags": filtered_tags
            })

        return filtered_data

    return data

def train_ner_model():
    """Main training function optimized for Kaggle."""
    logger.info("🚀 Starting Kaggle NER Training")
    
    # Environment setup
    is_kaggle = detect_kaggle_environment()
    gpu_memory = get_gpu_info()
    batch_size = get_optimal_batch_size(gpu_memory)
    
    logger.info(f"Environment: {'Kaggle' if is_kaggle else 'Local'}")
    logger.info(f"Batch size: {batch_size}")
    
    # Get paths
    training_data_path, output_path = get_kaggle_paths()
    logger.info(f"Training data: {training_data_path}")
    logger.info(f"Output path: {output_path}")
    
    # Load training data
    max_samples = 1000 if is_kaggle else None  # Limit for Kaggle
    train_data = load_and_convert_data(training_data_path, max_samples)

    if len(train_data) < 10:
        raise ValueError(f"Insufficient training data: {len(train_data)} samples")

    # Validate data distribution to prevent training failures
    train_data = validate_data_distribution(train_data)

    # Split data
    train_data, eval_data = train_test_split(train_data, test_size=0.2, random_state=42)
    logger.info(f"Train: {len(train_data)}, Eval: {len(eval_data)}")
    
    # Extract labels
    labels = set()
    for sample in train_data + eval_data:
        for tag in sample["ner_tags"]:
            if tag != "O":
                labels.add(tag)
    
    labels = sorted(list(labels))
    logger.info(f"Labels: {labels}")
    
    if not labels:
        labels = ["B-PERSON", "I-PERSON", "B-ORG", "I-ORG", "B-LOC", "I-LOC"]
        logger.warning(f"No labels found, using default: {labels}")
    
    # Create datasets
    train_dataset = Dataset.from_list(train_data)
    eval_dataset = Dataset.from_list(eval_data)
    
    # Initialize model
    logger.info("Initializing SpanMarker model...")
    model = SpanMarkerModel.from_pretrained(
        "microsoft/deberta-v3-base",
        labels=labels,
        model_max_length=256,  # Reduced for Kaggle
        entity_max_length=8
    )
    
    # Move to GPU if available
    if torch.cuda.is_available():
        model = model.cuda()
        logger.info("Model moved to GPU")
    
    # Training arguments optimized for Kaggle
    output_path.mkdir(parents=True, exist_ok=True)
    
    args = TrainingArguments(
        output_dir=str(output_path),
        learning_rate=5e-5,
        per_device_train_batch_size=batch_size,
        per_device_eval_batch_size=batch_size,
        num_train_epochs=2,  # Reduced for Kaggle
        warmup_ratio=0.1,
        weight_decay=0.01,
        fp16=torch.cuda.is_available(),
        save_strategy="epoch",
        eval_strategy="epoch",
        logging_steps=10,
        save_total_limit=2,
        load_best_model_at_end=True,
        metric_for_best_model="eval_f1",
        greater_is_better=True,
        report_to="none",
        dataloader_num_workers=0,  # Avoid multiprocessing issues in Kaggle
        remove_unused_columns=False,
    )
    
    # Create trainer
    trainer = SpanMarkerTrainer(
        model=model,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        args=args,
        callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
    )
    
    # Train
    logger.info("🏋️ Starting training...")
    start_time = time.time()
    
    try:
        trainer.train()
        training_time = time.time() - start_time
        logger.info(f"✅ Training completed in {training_time:.1f} seconds")
        
        # Save model
        trainer.save_model(str(output_path))
        logger.info(f"✅ Model saved to {output_path}")
        
        # Save entity types
        entity_types_path = output_path / "entity_types.json"
        with open(entity_types_path, 'w') as f:
            json.dump({"entity_types": labels}, f, indent=2)
        
        # Evaluate
        eval_results = trainer.evaluate()
        logger.info(f"📊 Evaluation results: {eval_results}")
        
        # Clean up GPU memory
        del model, trainer
        torch.cuda.empty_cache()
        gc.collect()
        
        logger.info("🎉 Training completed successfully!")
        return {
            "model_path": str(output_path),
            "entity_types": labels,
            "training_samples": len(train_data),
            "eval_results": eval_results
        }
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise

if __name__ == "__main__":
    try:
        result = train_ner_model()
        print("\n" + "="*50)
        print("🎉 TRAINING COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"Model saved to: {result['model_path']}")
        print(f"Entity types: {len(result['entity_types'])}")
        print(f"Training samples: {result['training_samples']}")
        print("="*50)
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
