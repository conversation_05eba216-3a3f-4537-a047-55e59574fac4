#!/usr/bin/env python3
"""
Complete Kaggle setup and training script for NER model.
This script handles all dependencies, data preparation, and training in Kaggle kernels.
"""

import os
import sys
import subprocess
import json
import logging
import warnings
from pathlib import Path

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_DISABLED"] = "true"

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_dependencies():
    """Install all required dependencies for Kaggle."""
    logger.info("🔧 Installing dependencies...")
    
    dependencies = [
        "span-marker>=1.5.0",
        "transformers>=4.30.0", 
        "torch>=1.13.0",
        "datasets>=2.10.0",
        "scikit-learn>=1.2.0",
        "psutil>=5.9.0",
        "accelerate>=0.20.0"
    ]
    
    for dep in dependencies:
        try:
            # Check if already installed
            pkg_name = dep.split('>=')[0].replace('-', '_')
            __import__(pkg_name)
            logger.info(f"✓ {dep} already available")
        except ImportError:
            logger.info(f"📦 Installing {dep}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep, 
                "--quiet", "--no-warn-script-location"
            ])
            logger.info(f"✅ {dep} installed")

def setup_kaggle_environment():
    """Setup Kaggle environment and paths."""
    logger.info("🏗️ Setting up Kaggle environment...")
    
    # Detect Kaggle
    is_kaggle = any([
        os.path.exists('/kaggle'),
        'KAGGLE_KERNEL_RUN_TYPE' in os.environ,
        '/kaggle' in os.getcwd()
    ])
    
    if is_kaggle:
        logger.info("✅ Kaggle environment detected")
        working_dir = Path('/kaggle/working')
        input_dir = Path('/kaggle/input')
        
        # Create necessary directories
        (working_dir / 'data' / 'models').mkdir(parents=True, exist_ok=True)
        (working_dir / 'logs').mkdir(parents=True, exist_ok=True)
        
        return working_dir, input_dir, True
    else:
        logger.info("🏠 Local environment detected")
        return Path('.'), Path('.'), False

def create_training_data(output_path: Path):
    """Create comprehensive training data for NER."""
    logger.info("📝 Creating training data...")
    
    # Comprehensive training data with proper distribution
    training_samples = []
    
    # Person entities (50 samples)
    person_samples = [
        {"text": f"John Smith {i} is a software engineer.", "entities": [[0, 10+len(str(i)), "PERSON"]]},
        {"text": f"Sarah Johnson {i} works in marketing.", "entities": [[0, 13+len(str(i)), "PERSON"]]},
        {"text": f"Michael Brown {i} leads the team.", "entities": [[0, 13+len(str(i)), "PERSON"]]},
        {"text": f"Lisa Davis {i} manages the project.", "entities": [[0, 10+len(str(i)), "PERSON"]]},
        {"text": f"David Wilson {i} is the CEO.", "entities": [[0, 12+len(str(i)), "PERSON"]]},
    ]
    
    # Organization entities (50 samples)  
    org_samples = [
        {"text": f"Microsoft {i} develops software.", "entities": [[0, 9+len(str(i)), "ORG"]]},
        {"text": f"Google {i} is a tech company.", "entities": [[0, 6+len(str(i)), "ORG"]]},
        {"text": f"Apple {i} makes smartphones.", "entities": [[0, 5+len(str(i)), "ORG"]]},
        {"text": f"Amazon {i} sells online.", "entities": [[0, 6+len(str(i)), "ORG"]]},
        {"text": f"Tesla {i} builds electric cars.", "entities": [[0, 5+len(str(i)), "ORG"]]},
    ]
    
    # Location entities (50 samples)
    loc_samples = [
        {"text": f"Seattle {i} is a beautiful city.", "entities": [[0, 7+len(str(i)), "LOC"]]},
        {"text": f"New York {i} never sleeps.", "entities": [[0, 8+len(str(i)), "LOC"]]},
        {"text": f"California {i} has great weather.", "entities": [[0, 10+len(str(i)), "LOC"]]},
        {"text": f"London {i} is historic.", "entities": [[0, 6+len(str(i)), "LOC"]]},
        {"text": f"Tokyo {i} is very modern.", "entities": [[0, 5+len(str(i)), "LOC"]]},
    ]
    
    # Generate samples
    for i in range(10):
        training_samples.extend([
            person_samples[i % len(person_samples)],
            org_samples[i % len(org_samples)], 
            loc_samples[i % len(loc_samples)]
        ])
    
    # Mixed entity samples
    mixed_samples = [
        {"text": "John works at Microsoft in Seattle.", "entities": [[0, 4, "PERSON"], [14, 23, "ORG"], [27, 34, "LOC"]]},
        {"text": "Sarah joined Google in California.", "entities": [[0, 5, "PERSON"], [13, 19, "ORG"], [23, 33, "LOC"]]},
        {"text": "The Apple store in New York is busy.", "entities": [[4, 9, "ORG"], [19, 27, "LOC"]]},
        {"text": "Amazon delivery to London was fast.", "entities": [[0, 6, "ORG"], [19, 25, "LOC"]]},
        {"text": "Tesla factory in Austin employs many.", "entities": [[0, 5, "ORG"], [17, 23, "LOC"]]},
    ]
    
    training_samples.extend(mixed_samples * 4)  # 20 more samples
    
    # Write to file
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        for sample in training_samples:
            f.write(json.dumps(sample) + '\n')
    
    logger.info(f"✅ Created {len(training_samples)} training samples")
    return len(training_samples)

def main():
    """Main execution function."""
    logger.info("🚀 Starting Kaggle NER Training Setup")
    
    try:
        # Step 1: Install dependencies
        install_dependencies()
        
        # Step 2: Setup environment
        working_dir, input_dir, is_kaggle = setup_kaggle_environment()
        
        # Step 3: Import libraries after installation
        import torch
        from datasets import Dataset
        from sklearn.model_selection import train_test_split
        from span_marker import SpanMarkerModel, SpanMarkerTrainer
        from transformers import TrainingArguments, EarlyStoppingCallback
        
        logger.info(f"🔥 PyTorch version: {torch.__version__}")
        logger.info(f"🎯 CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            logger.info(f"🚀 GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        
        # Step 4: Prepare training data
        if is_kaggle:
            # Look for existing data in input
            training_data_path = None
            for path in input_dir.rglob('*.jsonl'):
                if 'entity' in path.name.lower() or 'training' in path.name.lower():
                    training_data_path = path
                    break
            
            if not training_data_path:
                training_data_path = working_dir / 'entity_training_data.jsonl'
                create_training_data(training_data_path)
            
            output_path = working_dir / 'ner_model'
        else:
            training_data_path = Path('data/training/entity_training_data.jsonl')
            output_path = Path('data/models/ner_model')
            
            if not training_data_path.exists():
                create_training_data(training_data_path)
        
        logger.info(f"📂 Training data: {training_data_path}")
        logger.info(f"💾 Output path: {output_path}")
        
        # Step 5: Load and process data
        logger.info("📖 Loading training data...")
        training_data = []
        
        with open(training_data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    text = data["text"]
                    entities = data["entities"]
                    
                    # Simple tokenization
                    tokens = text.split()
                    ner_tags = ["O"] * len(tokens)
                    
                    # Map entities to tokens
                    for start_char, end_char, label in entities:
                        entity_text = text[start_char:end_char]
                        
                        # Find overlapping tokens
                        current_pos = 0
                        first_token = True
                        
                        for i, token in enumerate(tokens):
                            token_start = text.find(token, current_pos)
                            if token_start == -1:
                                continue
                            token_end = token_start + len(token)
                            
                            # Check overlap
                            if token_start < end_char and token_end > start_char:
                                if first_token:
                                    ner_tags[i] = f"B-{label}"
                                    first_token = False
                                else:
                                    ner_tags[i] = f"I-{label}"
                            
                            current_pos = token_end
                    
                    if tokens and ner_tags:
                        training_data.append({
                            "tokens": tokens,
                            "ner_tags": ner_tags
                        })
                        
                except Exception as e:
                    logger.warning(f"Error processing line {line_num}: {e}")
        
        logger.info(f"✅ Loaded {len(training_data)} samples")
        
        if len(training_data) < 20:
            raise ValueError(f"Insufficient training data: {len(training_data)} samples")
        
        # Step 6: Validate data distribution
        from collections import Counter
        label_counts = Counter()
        for sample in training_data:
            for tag in sample["ner_tags"]:
                if tag != "O":
                    label_counts[tag] += 1
        
        logger.info(f"📊 Label distribution: {dict(label_counts)}")
        
        # Remove insufficient labels
        insufficient_labels = [label for label, count in label_counts.items() if count < 2]
        if insufficient_labels:
            logger.warning(f"⚠️ Removing labels with <2 samples: {insufficient_labels}")
            
            filtered_data = []
            for sample in training_data:
                filtered_tags = ["O" if tag in insufficient_labels else tag for tag in sample["ner_tags"]]
                filtered_data.append({"tokens": sample["tokens"], "ner_tags": filtered_tags})
            training_data = filtered_data
        
        # Step 7: Split data
        train_data, eval_data = train_test_split(training_data, test_size=0.2, random_state=42)
        logger.info(f"📊 Train: {len(train_data)}, Eval: {len(eval_data)}")
        
        # Step 8: Extract labels
        labels = set()
        for sample in train_data + eval_data:
            for tag in sample["ner_tags"]:
                if tag != "O":
                    labels.add(tag)
        
        labels = sorted(list(labels))
        logger.info(f"🏷️ Labels: {labels}")
        
        if not labels:
            labels = ["B-PERSON", "I-PERSON", "B-ORG", "I-ORG", "B-LOC", "I-LOC"]
            logger.warning(f"⚠️ No labels found, using defaults: {labels}")
        
        # Step 9: Create datasets
        train_dataset = Dataset.from_list(train_data)
        eval_dataset = Dataset.from_list(eval_data)
        
        # Step 10: Initialize model
        logger.info("🤖 Initializing SpanMarker model...")
        model = SpanMarkerModel.from_pretrained(
            "microsoft/deberta-v3-base",
            labels=labels,
            model_max_length=256,
            entity_max_length=8
        )
        
        if torch.cuda.is_available():
            model = model.cuda()
            logger.info("🚀 Model moved to GPU")
        
        # Step 11: Setup training
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Determine batch size
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            batch_size = 4 if gpu_memory >= 15 else 2
        else:
            batch_size = 1
        
        logger.info(f"📦 Batch size: {batch_size}")
        
        args = TrainingArguments(
            output_dir=str(output_path),
            learning_rate=5e-5,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            num_train_epochs=3,
            warmup_ratio=0.1,
            weight_decay=0.01,
            fp16=torch.cuda.is_available(),
            save_strategy="epoch",
            eval_strategy="epoch",
            logging_steps=10,
            save_total_limit=2,
            load_best_model_at_end=True,
            metric_for_best_model="eval_f1",
            greater_is_better=True,
            report_to="none",
            dataloader_num_workers=0,
            remove_unused_columns=False,
        )
        
        # Step 12: Create trainer and train
        trainer = SpanMarkerTrainer(
            model=model,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            args=args,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
        )
        
        logger.info("🏋️ Starting training...")
        trainer.train()
        
        # Step 13: Save model
        trainer.save_model(str(output_path))
        
        # Save entity types
        with open(output_path / "entity_types.json", 'w') as f:
            json.dump({"entity_types": labels}, f, indent=2)
        
        # Step 14: Evaluate
        eval_results = trainer.evaluate()
        logger.info(f"📊 Final results: {eval_results}")
        
        logger.info("🎉 Training completed successfully!")
        
        print("\n" + "="*60)
        print("🎉 NER MODEL TRAINING COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"📂 Model saved to: {output_path}")
        print(f"🏷️ Entity types: {len(labels)}")
        print(f"📊 Training samples: {len(train_data)}")
        print(f"📈 Evaluation F1: {eval_results.get('eval_f1', 'N/A')}")
        print("="*60)
        
        return {
            "model_path": str(output_path),
            "entity_types": labels,
            "training_samples": len(train_data),
            "eval_results": eval_results
        }
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    main()
