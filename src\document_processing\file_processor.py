"""
Process different file types for document ingestion.
"""
import os
import re
import unicodedata
from typing import Dict, Any, Callable
from pathlib import Path
import logging
from PIL import Image
from .ocr_processor import OCRProcessor

try:
    import docx
except ImportError:
    docx = None

try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from pptx import Presentation
except ImportError:
    Presentation = None

try:
    import textract
except ImportError:
    textract = None

try:
    import extract_msg
except ImportError:
    extract_msg = None

try:
    import markdown
    from bs4 import BeautifulSoup
    markdown_available = True
except ImportError:
    markdown_available = False
    try:
        from bs4 import BeautifulSoup
    except ImportError:
        BeautifulSoup = None

logger = logging.getLogger(__name__)

# Initialize PaddleOCR processor globally for reuse
ocr_processor = OCRProcessor()

# Add this function to restructure OCR-extracted text

def refactor_ocr_text(text: str) -> str:
    lines = text.splitlines()
    output = []
    top_level_counter = 1
    last_top_level = None
    subpoint_stack = []

    # Regex patterns
    top_level_pattern = re.compile(r'^(?:\d+\.)\s+')
    subpoint_pattern = re.compile(r'^(\d+\.\d+|[a-zA-Z]\)|[ivxlc]+\.)\s+')
    bullet_pattern = re.compile(r'^([•–-])\s+')
    non_numeric_header_pattern = re.compile(r'^(SECTION [A-Z]|Q\d+)\s*', re.IGNORECASE)

    for line in lines:
        stripped = line.strip()
        if not stripped:
            output.append("")
            continue

        # Top-level point (e.g., "1. ...")
        if top_level_pattern.match(stripped):
            # Renumber
            content = top_level_pattern.sub('', stripped)
            output.append(f"{top_level_counter}. {content}")
            last_top_level = top_level_counter
            top_level_counter += 1
            subpoint_stack = []
        # Subpoint (e.g., "1.1 ...", "a) ...", "i. ...")
        elif subpoint_pattern.match(stripped):
            match = subpoint_pattern.match(stripped)
            indent = "    "
            output.append(f"{indent}{stripped}")
            subpoint_stack.append(stripped)
        # Bullet or dash
        elif bullet_pattern.match(stripped):
            indent = "        "
            output.append(f"{indent}{stripped}")
        # Non-numeric header (e.g., "SECTION A", "Q1")
        elif non_numeric_header_pattern.match(stripped):
            output.append(stripped)
            last_top_level = None
            subpoint_stack = []
        # Otherwise, treat as normal text (keep indentation if under subpoint)
        else:
            if subpoint_stack:
                indent = "    "
                output.append(f"{indent}{stripped}")
            else:
                output.append(stripped)

    return "\n".join(output)

def renumber_faq(text):
    # Split on blocks that start with a number and a dot, keep the delimiter
    chunks = re.split(r'(?=^\d+\. )', text, flags=re.MULTILINE)
    output = []
    q_num = 1
    for chunk in chunks:
        chunk = chunk.strip()
        if not chunk:
            continue
        # Replace the leading number with the new number
        chunk = re.sub(r'^\d+\.', f'{q_num}.', chunk, count=1)
        output.append(chunk)
        q_num += 1
    return '\n\n'.join(output)

class FileProcessor:
    """Process different file types for text extraction."""

    MAX_FILE_SIZE_MB = 20

    @staticmethod
    def _get_handler(extension: str) -> Callable:
        return {
            '.pdf': FileProcessor.extract_from_pdf,
            '.docx': FileProcessor.extract_from_docx,
            '.doc': FileProcessor.extract_from_doc,
            '.txt': FileProcessor.extract_from_txt,
            '.md': FileProcessor.extract_from_markdown,
            '.csv': FileProcessor.extract_from_csv,
            '.xls': FileProcessor.extract_from_excel,
            '.xlsx': FileProcessor.extract_from_excel,
            '.pptx': FileProcessor.extract_from_pptx,
            '.html': FileProcessor.extract_from_html,
            '.htm': FileProcessor.extract_from_html,
            '.msg': FileProcessor.extract_from_msg,
            '.jpg': FileProcessor.extract_from_image,
            '.jpeg': FileProcessor.extract_from_image,
            '.png': FileProcessor.extract_from_image,
        }.get(extension, FileProcessor.extract_from_txt)

    @staticmethod
    def _available_extensions() -> set:
        exts = set()
        if fitz: 
            exts.add('.pdf')
        if docx: 
            exts.add('.docx')
        if textract: 
            exts.add('.doc')
        if pd: 
            exts.update({'.csv', '.xls', '.xlsx'})
        if Presentation: 
            exts.add('.pptx')
        if markdown_available: 
            exts.add('.md')
        exts.update({'.txt'})
        if BeautifulSoup:
            exts.update({'.html', '.htm'})
        if extract_msg: 
            exts.add('.msg')
        # OCRProcessor supports images
        exts.update({'.jpg', '.jpeg', '.png'})
        return exts

    SUPPORTED_EXTENSIONS = _available_extensions()

    @staticmethod
    def process_file(file_path: Path) -> Dict[str, Any]:
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return FileProcessor._error_doc(file_path, "FILE NOT FOUND")
        if file_path.stat().st_size > FileProcessor.MAX_FILE_SIZE_MB * 1024 * 1024:
            logger.warning(f"File too large: {file_path.name}")
            return FileProcessor._error_doc(file_path, "FILE TOO LARGE")
        ext = file_path.suffix.lower()
        title = FileProcessor._generate_title(file_path.name, ext)
        if ext not in FileProcessor.SUPPORTED_EXTENSIONS:
            logger.warning(f"Unsupported file type: {ext} for file {file_path.name}")
            return FileProcessor._error_doc(file_path, f"UNSUPPORTED FILE TYPE: {ext}", title)
        handler = FileProcessor._get_handler(ext)
        try:
            content = handler(file_path)  # No normalization, return as-is
            # --- Restructure extracted text for logical hierarchy and numbering ---
            content = refactor_ocr_text(content)
            if not content:
                content = f"[EMPTY CONTENT: {file_path.name}]"
            elif len(content) < 50:
                logger.warning(f"Very short content extracted from {file_path} ({len(content)} characters)")
            logger.info(f"Processed {ext.upper()} file: {file_path.name} ({len(content)} characters)")
            return {
                "title": title,
                "content": content,
                "source_file": str(file_path),
                "file_type": ext.lstrip('.')
            }
        except (IOError, UnicodeDecodeError, ValueError) as e:
            logger.error(f"Error processing file {file_path}: {e}")
            return FileProcessor._error_doc(file_path, str(e), title)
        except Exception as e:
            logger.exception(f"Unexpected error processing file {file_path}")
            return FileProcessor._error_doc(file_path, f"UNEXPECTED: {e}", title)

    @staticmethod
    def _generate_title(file_name: str, file_extension: str) -> str:
        title = file_name.replace(file_extension, '').replace('-', ' ').replace('_', ' ')
        return re.sub(r'\s+', ' ', title).strip().title()

    @staticmethod
    def _error_doc(file_path: Path, error: str, title: str = None) -> Dict[str, Any]:
        return {
            "title": title or f"Error: {file_path.name}",
            "content": f"[{error}: {file_path.name}]",
            "source_file": str(file_path),
            "file_type": "error"
        }

    @staticmethod
    def extract_from_pdf(file_path: Path) -> str:
        if fitz is None:
            return "[PDF support not installed - install PyMuPDF: pip install PyMuPDF]"
        all_text = []
        try:
            with fitz.open(file_path) as pdf:
                if pdf.is_encrypted:
                    raise IOError(f"PDF is encrypted: {file_path.name}")
                for page_num, page in enumerate(pdf, 1):
                    page_text = page.get_text()
                    # If text is empty or <500 chars, use DocTR OCR
                    if not page_text.strip() or len(page_text.strip()) < 500:
                        # Save page as image
                        pix = page.get_pixmap()
                        img_path = Path(f"/tmp/{file_path.stem}_page{page_num}.png")
                        pix.save(str(img_path))
                        try:
                            ocr_result = ocr_processor.extract_text_with_layout(img_path)
                            # Combine all block text and tables
                            blocks = [b['text'] for b in ocr_result['blocks'] if 'text' in b]
                            tables = [str(t) for t in ocr_result['tables']]
                            ocr_text = '\n'.join(blocks + tables)
                            if ocr_text.strip():
                                page_text += "\n[OCR]\n" + ocr_text.strip()
                        except Exception as ocr_err:
                            logger.warning(f"DocTR OCR failed for page {page_num} in {file_path.name}: {ocr_err}")
                        finally:
                            try:
                                img_path.unlink()
                            except Exception:
                                pass
                    all_text.append(page_text)
            text = "\n\n".join([t for t in all_text if t.strip()])
            return text
        except Exception as e:
            raise IOError(f"PDF read failure: {e}")

    @staticmethod
    def extract_from_docx(file_path: Path) -> str:
        """Extract text from DOCX file."""
        if docx is None:
            return "[DOCX support not installed - install: pip install python-docx]"
        try:
            doc = docx.Document(file_path)
            paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
            return "\n".join(paragraphs)
        except Exception as e:
            raise IOError(f"DOCX read failure: {e}")

    @staticmethod
    def extract_from_doc(file_path: Path) -> str:
        """Extract text from DOC file using textract."""
        if textract is None:
            return "[DOC support not installed - install: pip install textract]"
        try:
            content = textract.process(str(file_path))
            return content.decode('utf-8', errors='replace')
        except Exception as e:
            raise ValueError(f"Textract failed: {e}")

    @staticmethod
    def extract_from_txt(file_path: Path) -> str:
        """Extract text from plain text file with encoding detection. Return raw content as-is for formatting preservation."""
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc) as f:
                    return f.read()  # No normalization, return raw
            except UnicodeDecodeError:
                continue
        
        # Last resort: read as binary and replace errors
        try:
            with open(file_path, 'rb') as f:
                return f.read().decode('utf-8', errors='replace')
        except Exception as e:
            raise IOError(f"Failed to read text file: {e}")

    @staticmethod
    def extract_from_markdown(file_path: Path) -> str:
        """Extract text from Markdown file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                md_text = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                md_text = f.read()
        
        if not markdown_available:
            return md_text  # Return raw markdown if parser not available
        
        try:
            html = markdown.markdown(md_text)
            return BeautifulSoup(html, 'html.parser').get_text()
        except Exception:
            return md_text  # Fallback to raw markdown

    @staticmethod
    def extract_from_csv(file_path: Path) -> str:
        """Extract text from CSV file."""
        if pd is None:
            return "[Pandas not installed for CSV processing - install: pip install pandas]"
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            return df.to_string(index=False)
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='latin-1')
            return df.to_string(index=False)
        except Exception as e:
            raise IOError(f"CSV read failure: {e}")

    @staticmethod
    def extract_from_excel(file_path: Path) -> str:
        """Extract text from Excel file."""
        if pd is None:
            return "[Pandas not installed for Excel processing - install: pip install pandas openpyxl]"
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            sheets_content = []
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                if not df.empty:
                    sheets_content.append(f"=== Sheet: {sheet_name} ===")
                    sheets_content.append(df.to_string(index=False))
            return "\n\n".join(sheets_content)
        except Exception as e:
            raise IOError(f"Excel read failure: {e}")

    @staticmethod
    def extract_from_pptx(file_path: Path) -> str:
        """Extract text from PowerPoint file."""
        if Presentation is None:
            return "[PPTX support not installed - install: pip install python-pptx]"
        try:
            prs = Presentation(file_path)
            slides_content = []
            for i, slide in enumerate(prs.slides, 1):
                slide_text = []
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())
                if slide_text:
                    slides_content.append(f"=== Slide {i} ===")
                    slides_content.extend(slide_text)
            return "\n".join(slides_content)
        except Exception as e:
            raise IOError(f"PPTX read failure: {e}")

    @staticmethod
    def extract_from_html(file_path: Path) -> str:
        """Extract text from HTML file."""
        if BeautifulSoup is None:
            return "[HTML support not installed - install: pip install beautifulsoup4]"
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        return soup.get_text(separator='\n', strip=True)

    @staticmethod
    def extract_from_msg(file_path: Path) -> str:
        """Extract text from Outlook MSG file."""
        if extract_msg is None:
            return "[MSG support not installed - install: pip install extract-msg]"
        try:
            msg = extract_msg.Message(str(file_path))
            content_parts = []
            if msg.subject:
                content_parts.append(f"Subject: {msg.subject}")
            if msg.body:
                content_parts.append(f"Body: {msg.body}")
            return "\n".join(content_parts)
        except Exception as e:
            raise IOError(f"MSG read failure: {e}")

    @staticmethod
    def extract_from_image(file_path: Path) -> str:
        try:
            ocr_result = ocr_processor.extract_text_with_layout(file_path)
            blocks = [b['text'] for b in ocr_result['blocks'] if 'text' in b]
            tables = [str(t) for t in ocr_result['tables']]
            return '\n'.join(blocks + tables)
        except Exception as e:
            raise IOError(f"DocTR image extraction failed: {e}")