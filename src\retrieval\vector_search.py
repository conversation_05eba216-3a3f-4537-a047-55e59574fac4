"""
Vector similarity search for document retrieval using Qdrant.
"""
from typing import List, Dict, Any, Optional
import numpy as np
import time
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, wait_exponential

from ..utils.logger import get_logger
from ..database.vector_store import QdrantVectorStore, VectorStoreError # Import VectorStoreError
from ..document_processing.embedding_generator import EmbeddingGenerator
from ..config import SIMILARITY_THRESHOLD, MAX_VECTOR_SEARCH_TOP_K # Ensure these are well-defined

logger = get_logger(__name__)

# Define custom exceptions for more granular error handling
class SearchServiceError(Exception):
    """Custom exception for errors specifically within the VectorSearch service."""
    pass

class EmbeddingGenerationError(SearchServiceError):
    """Exception for failures during embedding generation."""
    pass

class VectorStoreSearchError(SearchServiceError):
    """Exception for failures during vector store search."""
    pass

# Add singleton getters (import from app.py or define here if needed)
def get_embedding_generator():
    from app import get_embedding_generator as app_get_embedding_generator
    return app_get_embedding_generator()
def get_vector_store():
    from app import get_vector_store as app_get_vector_store
    return app_get_vector_store()

class VectorSearch:
    """Vector similarity search for document retrieval using Qdrant.
    Orchestrates embedding generation and Qdrant search with post-processing.
    """

    def __init__(self,
                 vector_store: Optional[QdrantVectorStore] = None,
                 embedding_generator: Optional[EmbeddingGenerator] = None):
        # Initialize dependencies, allowing for dependency injection for testing
        try:
            self.vector_store = vector_store or get_vector_store()
            self.embedding_generator = embedding_generator or get_embedding_generator()
            logger.info("VectorSearch initialized successfully.")
        except Exception as e:
            logger.critical(f"Failed to initialize VectorSearch components: {e}", exc_info=True)
            raise SearchServiceError("Failed to initialize vector search service.") from e

    # Apply exponential backoff for retries, which is generally better for services
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10),
           retry=retry_if_exception_type(Exception), reraise=True)
    def _generate_embedding(self, query: str) -> np.ndarray:
        """Generates an embedding for the given query with retry logic."""
        logger.info(f"Starting embedding generation for query (first 50 chars): '{query[:50]}'")
        try:
            import asyncio
            import concurrent.futures
            
            # Add timeout to embedding generation
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(self.embedding_generator.generate_query_embedding, query)
                embedding = future.result(timeout=15.0)  # 15 second timeout
                
            if not isinstance(embedding, np.ndarray) or embedding.ndim == 0 or embedding.size == 0:
                raise ValueError("Generated embedding is not a valid numpy array.")
            logger.info(f"Successfully generated embedding with shape: {embedding.shape}")
            return embedding
        except concurrent.futures.TimeoutError:
            logger.error("Embedding generation timed out after 15 seconds")
            raise EmbeddingGenerationError("Embedding generation timed out")
        except Exception as e:
            logger.error(f"Failed to generate embedding for query: {str(e)}", exc_info=True)
            raise EmbeddingGenerationError(f"Embedding generation failed: {str(e)}") from e

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10),
           retry=retry_if_exception_type((VectorStoreError, Exception)), reraise=True) # Retry on VectorStoreError
    def _vector_store_search(self, query_embedding: np.ndarray, top_k: int) -> List[Dict[str, Any]]:
        """Performs search against the vector store with retry logic."""
        logger.debug(f"Attempting vector store search with top_k={top_k}")
        try:
            results = self.vector_store.search(query_embedding=query_embedding, top_k=top_k)
            logger.debug(f"Vector store search returned {len(results)} results.")
            return results
        except VectorStoreError as e: # Catch specific VectorStoreError
            logger.error(f"Vector store specific error during search: {str(e)}", exc_info=True)
            raise VectorStoreSearchError(f"Vector store search failed: {str(e)}") from e
        except Exception as e:
            logger.error(f"General error during vector store search: {str(e)}", exc_info=True)
            raise VectorStoreSearchError(f"Vector store search failed: {str(e)}") from e

    def search(self, query: str, top_k: int = 3, prioritize_files: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Search for documents similar to the query, applying relevance filtering and prioritization.
        """
        search_start_time = time.time()
        prioritize_files = prioritize_files or []

        if not isinstance(query, str) or not query.strip():
            logger.warning("Invalid or empty query received for search.")
            return []

        try:
            # 1. Generate embedding
            logger.info(f"Starting embedding generation for query: '{query[:50]}...'")
            query_embedding = self._generate_embedding(query)
            logger.info(f"Embedding generation completed successfully")

            # 2. Fetch a large candidate pool (high-recall)
            logger.info(f"Starting vector store search with candidate_k={max(top_k * 5, 30)}")
            candidate_k = max(top_k * 5, 30)
            results = self._vector_store_search(query_embedding, top_k=candidate_k)
            logger.info(f"Vector store search completed, got {len(results)} results")

            if not results:
                logger.info("No results returned from Qdrant.")
                return []

            valid_results = []
            for doc in results:
                if not isinstance(doc, dict) or "score" not in doc:
                    continue
                score = doc["score"]
                if not isinstance(score, (int, float)):
                    continue
                doc_copy = doc.copy()
                doc_copy["original_score"] = score
                valid_results.append(doc_copy)

            if not valid_results:
                return []

            # 3. Dynamic threshold filtering (loose)
            max_score = max(doc["score"] for doc in valid_results)
            min_allowed = max(SIMILARITY_THRESHOLD, max_score * 0.6)
            filtered = [doc for doc in valid_results if doc["score"] >= min_allowed]

            if len(filtered) < 2:
                filtered = valid_results[:top_k * 2]

            # 4. Boost scores for prioritized files
            for doc in filtered:
                source_file = doc.get("source_file", "")
                for pf in prioritize_files:
                    if pf and pf.lower() in source_file.lower():
                        doc["score"] *= 1.2
                        doc["prioritized"] = True
                        break
                else:
                    doc["prioritized"] = False

            # 5. Optional local reranking
            for doc in filtered:
                title_len = len(doc.get("title", ""))
                doc["rerank_score"] = doc["score"] - 0.01 * title_len

            # 6. Sort by reranked score
            filtered.sort(key=lambda d: d.get("rerank_score", d["score"]), reverse=True)
            final = filtered[:top_k]

            logger.info("Search completed", extra={
                "final_count": len(final),
                "max_score": max_score,
                "threshold_used": min_allowed,
                "prioritized_hits": sum(1 for d in final if d["prioritized"])
            })

            return final

        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}", exc_info=True)
            return []
