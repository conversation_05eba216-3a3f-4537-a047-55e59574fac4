import sqlite3
from datetime import datetime
from pathlib import Path
from ..utils.logger import get_logger
from ..config import USER_DB_PATH

logger = get_logger(__name__)

class SessionDatabase:
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self._ensure_tables()

    def _get_connection(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.db_path, isolation_level=None)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    browser TEXT,
                    os TEXT,
                    device_fingerprint TEXT,
                    login_time TIMESTAMP,
                    last_activity TIMESTAMP,
                    logout_time TIMESTAMP,
                    location_country TEXT,
                    location_city TEXT,
                    latitude REAL,
                    longitude REAL,
                    auth_method TEXT,
                    success INTEGER DEFAULT 1,
                    user_type TEXT DEFAULT 'admin'
                )
            ''')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_type ON sessions(user_type)')
            conn.commit()
            logger.info("[OK] Sessions table initialized.")

class SessionModel:
    def __init__(self, db_path=USER_DB_PATH):
        self.db = SessionDatabase(db_path)

    def create_session(self, id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, success=True, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO sessions (id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, success, user_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, int(success), user_type
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.exception("[ERROR] Failed to create session")
            return False

    def get_active_sessions(self, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM sessions WHERE logout_time IS NULL AND user_type = ? ORDER BY last_activity DESC
                ''', (user_type,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch active sessions")
            return []

    def get_session_history(self, user_type="admin", limit=100, offset=0, filters=None):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM sessions WHERE user_type = ?'
                params = [user_type]
                if filters:
                    for key, value in filters.items():
                        if value is not None:
                            query += f' AND {key} = ?'
                            params.append(value)
                query += ' ORDER BY login_time DESC LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                cursor.execute(query, tuple(params))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch session history")
            return []

    def get_session_locations(self, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, user_id, device_fingerprint, login_time, location_country, location_city, latitude, longitude, user_type FROM sessions WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND user_type = ?
                ''', (user_type,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch session locations")
            return []

    def get_session_anomalies(self, user_type="admin", limit=100, offset=0):
        # Placeholder: In production, this would run anomaly detection logic
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                # Example: fetch failed logins, impossible travel, blacklisted IPs, etc.
                cursor.execute('''
                    SELECT * FROM sessions WHERE success = 0 AND user_type = ? ORDER BY login_time DESC LIMIT ? OFFSET ?
                ''', (user_type, limit, offset))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch session anomalies")
            return [] 