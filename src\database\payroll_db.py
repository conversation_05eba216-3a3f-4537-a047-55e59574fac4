"""
Payroll document database management using SQLite.
Handles storage and retrieval of payroll documents, extracted fields, and detected issues.
Optimized for speed with async support and caching.
"""

import sqlite3
import json
import logging
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
import threading

from ..config import DATA_DIR
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Database path
PAYROLL_DB_PATH = DATA_DIR / "db" / "payroll.db"

# Ensure database directory exists
PAYROLL_DB_PATH.parent.mkdir(parents=True, exist_ok=True)

# Global thread pool for async operations
_db_executor = ThreadPoolExecutor(max_workers=2)

# Thread-local storage for database connections
_thread_local = threading.local()

@dataclass
class PayrollDocument:
    """Represents a payroll document record."""
    id: Optional[int] = None
    employee_id: Optional[str] = None
    employee_name: Optional[str] = None
    organization: Optional[str] = None
    month: Optional[str] = None
    year: Optional[str] = None
    document_type: str = "payslip"
    file_path: Optional[str] = None
    file_hash: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    processing_status: str = "pending"  # pending, processed, failed, resolved
    confidence_score: float = 0.0
    escalation_triggered_at: Optional[str] = None
    auto_resolved_at: Optional[str] = None


@dataclass
class ParsedField:
    """Represents an extracted field from a payroll document."""
    id: Optional[int] = None
    document_id: int = None
    field_name: str = None
    field_value: Optional[str] = None
    confidence: float = 0.0
    extraction_method: str = "ner"  # ner, regex, manual
    flagged: bool = False
    created_at: Optional[str] = None


@dataclass
class DetectedIssue:
    """Represents a detected issue in a payroll document."""
    id: Optional[int] = None
    document_id: int = None
    issue_type: str = None  # bonus_missing, tax_error, salary_delay, etc.
    description: Optional[str] = None
    confidence: float = 0.0
    status: str = "open"  # open, resolved, dismissed
    resolved_by: Optional[str] = None
    resolved_at: Optional[str] = None
    created_at: Optional[str] = None


class PayrollDatabase:
    """
    SQLite database manager for payroll documents and extracted data.
    Optimized for speed with async support and connection pooling.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the payroll database.
        
        Args:
            db_path: Optional custom database path
        """
        self.db_path = db_path or str(PAYROLL_DB_PATH)
        self._init_database()
        self._cache = {}
        self._cache_lock = threading.Lock()
        logger.info(f"PayrollDatabase initialized at: {self.db_path}")
    
    def _get_connection(self):
        """Get a database connection with connection pooling."""
        if not hasattr(_thread_local, 'connection'):
            _thread_local.connection = sqlite3.connect(
                self.db_path, 
                check_same_thread=False,
                timeout=30.0
            )
            _thread_local.connection.row_factory = sqlite3.Row
        return _thread_local.connection
    
    @contextmanager
    def _get_connection_context(self):
        """Context manager for database connections."""
        conn = self._get_connection()
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            raise
        finally:
            # Don't close the connection, keep it in thread-local storage
            pass
    
    def _init_database(self):
        """Initialize database tables if they don't exist."""
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            
            # Create documents table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id TEXT,
                    employee_name TEXT,
                    organization TEXT,
                    month TEXT,
                    year TEXT,
                    document_type TEXT DEFAULT 'payslip',
                    file_path TEXT,
                    file_hash TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processing_status TEXT DEFAULT 'pending',
                    confidence_score REAL DEFAULT 0.0,
                    escalation_triggered_at TIMESTAMP,
                    auto_resolved_at TIMESTAMP
                )
            """)
            
            # Create parsed_fields table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS parsed_fields (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER NOT NULL,
                    field_name TEXT NOT NULL,
                    field_value TEXT,
                    confidence REAL DEFAULT 0.0,
                    extraction_method TEXT DEFAULT 'ner',
                    flagged BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
                )
            """)
            
            # Create detected_issues table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS detected_issues (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER NOT NULL,
                    issue_type TEXT NOT NULL,
                    description TEXT,
                    confidence REAL DEFAULT 0.0,
                    status TEXT DEFAULT 'open',
                    resolved_by TEXT,
                    resolved_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_documents_employee_id ON documents(employee_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_documents_month_year ON documents(month, year)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_documents_organization ON documents(organization)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_parsed_fields_document_id ON parsed_fields(document_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_parsed_fields_field_name ON parsed_fields(field_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_detected_issues_document_id ON detected_issues(document_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_detected_issues_status ON detected_issues(status)")
            
            conn.commit()
            logger.info("Database tables initialized successfully")
    
    async def insert_document_async(self, document: PayrollDocument) -> int:
        """Async version of insert_document."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.insert_document, document)
    
    def insert_document(self, document: PayrollDocument) -> int:
        """
        Insert a new document record.

        Args:
            document: PayrollDocument instance

        Returns:
            Document ID
        """
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO documents (
                    employee_id, employee_name, organization, month, year,
                    document_type, file_path, file_hash, processing_status, confidence_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                document.employee_id, document.employee_name, document.organization,
                document.month, document.year, document.document_type, document.file_path,
                document.file_hash, document.processing_status, document.confidence_score
            ))
            
            document_id = cursor.lastrowid
            conn.commit()
            
            # Clear cache
            with self._cache_lock:
                self._cache.clear()
            
            logger.info(f"Inserted document {document_id}")
            return document_id
    
    async def update_document_status_async(self, document_id: int, status: str, confidence_score: Optional[float] = None):
        """Async version of update_document_status."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.update_document_status, document_id, status, confidence_score)
    
    def update_document_status(self, document_id: int, status: str, confidence_score: Optional[float] = None):
        """
        Update document processing status.

        Args:
            document_id: Document ID
            status: New status (pending, processed, failed, resolved)
            confidence_score: Optional confidence score update
        """
        with self._get_connection_context() as conn:
            cursor = conn.cursor()

            if confidence_score is not None:
                cursor.execute("""
                    UPDATE documents
                    SET processing_status = ?, confidence_score = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (status, confidence_score, document_id))
            else:
                cursor.execute("""
                    UPDATE documents
                    SET processing_status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (status, document_id))

            conn.commit()
            
            # Clear cache
            with self._cache_lock:
                self._cache.clear()
            
            logger.info(f"Updated document {document_id} status to: {status}")
    
    async def track_escalation_triggered_async(self, document_id: int):
        """Async version of track_escalation_triggered."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.track_escalation_triggered, document_id)
    
    def track_escalation_triggered(self, document_id: int):
        """
        Track when escalation is triggered for a document.

        Args:
            document_id: Document ID
        """
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE documents
                SET escalation_triggered_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (document_id,))
            conn.commit()
            logger.info(f"Escalation triggered for document {document_id}")
    
    async def track_auto_resolution_async(self, document_id: int):
        """Async version of track_auto_resolution."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.track_auto_resolution, document_id)
    
    def track_auto_resolution(self, document_id: int):
        """
        Track when a document is auto-resolved.

        Args:
            document_id: Document ID
        """
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE documents
                SET auto_resolved_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (document_id,))
            conn.commit()
            logger.info(f"Auto-resolution tracked for document {document_id}")
    
    @lru_cache(maxsize=100)
    def get_document_by_id(self, document_id: int) -> Optional[PayrollDocument]:
        """
        Get a document by ID with caching.

        Args:
            document_id: Document ID

        Returns:
            PayrollDocument instance or None
        """
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM documents WHERE id = ?
            """, (document_id,))

            row = cursor.fetchone()
            if row:
                return PayrollDocument(**dict(row))
            return None
    
    async def get_document_by_id_async(self, document_id: int) -> Optional[PayrollDocument]:
        """Async version of get_document_by_id."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.get_document_by_id, document_id)
    
    def get_documents_by_employee(self, employee_id: str, limit: int = 50) -> List[PayrollDocument]:
        """
        Get all documents for an employee with caching.

        Args:
            employee_id: Employee ID
            limit: Maximum number of results

        Returns:
            List of PayrollDocument instances
        """
        cache_key = f"employee_{employee_id}_{limit}"
        
        with self._cache_lock:
            if cache_key in self._cache:
                return self._cache[cache_key]
        
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM documents
                WHERE employee_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (employee_id, limit))

            rows = cursor.fetchall()
            documents = [PayrollDocument(**dict(row)) for row in rows]
            
            # Cache the result
            with self._cache_lock:
                self._cache[cache_key] = documents
            
            return documents
    
    async def get_documents_by_employee_async(self, employee_id: str, limit: int = 50) -> List[PayrollDocument]:
        """Async version of get_documents_by_employee."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.get_documents_by_employee, employee_id, limit)
    
    def get_detected_issues(self, document_id: int, status: Optional[str] = None) -> List[DetectedIssue]:
        """
        Get detected issues for a document with caching.

        Args:
            document_id: Document ID
            status: Optional status filter (open, resolved, dismissed)

        Returns:
            List of DetectedIssue instances
        """
        cache_key = f"issues_{document_id}_{status}"
        
        with self._cache_lock:
            if cache_key in self._cache:
                return self._cache[cache_key]
        
        with self._get_connection_context() as conn:
            cursor = conn.cursor()

            if status:
                cursor.execute("""
                    SELECT * FROM detected_issues
                    WHERE document_id = ? AND status = ?
                    ORDER BY confidence DESC, created_at DESC
                """, (document_id, status))
            else:
                cursor.execute("""
                    SELECT * FROM detected_issues
                    WHERE document_id = ?
                    ORDER BY confidence DESC, created_at DESC
                """, (document_id,))

            rows = cursor.fetchall()
            issues = [DetectedIssue(**dict(row)) for row in rows]
            
            # Cache the result
            with self._cache_lock:
                self._cache[cache_key] = issues
            
            return issues
    
    async def get_detected_issues_async(self, document_id: int, status: Optional[str] = None) -> List[DetectedIssue]:
        """Async version of get_detected_issues."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.get_detected_issues, document_id, status)
    
    async def resolve_issue_async(self, issue_id: int, resolved_by: str):
        """Async version of resolve_issue."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(_db_executor, self.resolve_issue, issue_id, resolved_by)
    
    def resolve_issue(self, issue_id: int, resolved_by: str):
        """
        Mark an issue as resolved.

        Args:
            issue_id: Issue ID
            resolved_by: User who resolved the issue
        """
        with self._get_connection_context() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE detected_issues
                SET status = 'resolved', resolved_by = ?, resolved_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (resolved_by, issue_id))

            conn.commit()
            
            # Clear cache
            with self._cache_lock:
                self._cache.clear()
            
            logger.info(f"Issue {issue_id} resolved by {resolved_by}")
    
    def clear_cache(self):
        """Clear all cached data."""
        with self._cache_lock:
            self._cache.clear()
        self.get_document_by_id.cache_clear()
    
    def __del__(self):
        """Cleanup on destruction."""
        if hasattr(_thread_local, 'connection'):
            _thread_local.connection.close()
