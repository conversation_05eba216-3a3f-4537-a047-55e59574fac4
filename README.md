# Multi-Model RAG HR Chatbot Platform

## Overview
A full-stack, production-ready HR assistant platform combining advanced Retrieval-Augmented Generation (RAG) with a modern admin dashboard. The system provides accurate, context-aware answers to HR queries using company documents, supports voice and text interaction, and enables HR/admins to manage overrides and monitor activity via a React dashboard.

---

## Features
- **RAG Chatbot**: Answers HR questions using company documents and LLMs (Groq Llama 3)
- **Document Upload & Processing**: Supports PDF, DOCX, TXT, MD, CSV, XLS, PPTX, HTML, images (OCR)
- **Vector Search**: Fast retrieval using Qdrant
- **Voice Interaction**: Speech-to-text and text-to-speech
- **Admin Dashboard**: React UI for monitoring queries, chat logs, manual overrides, and document uploads
- **Manual Overrides**: <PERSON><PERSON> can set custom responses for specific patterns (SQLite-backed)
- **Authentication**: User registration, login, JWT, 2FA
- **Email Escalation**: Unanswered queries can be escalated to HR via email
- **Source Attribution**: Shows sources for each answer
- **History Management**: Per-user/device chat history
- **Production Logging & Monitoring**: Health checks, metrics, and structured logs

---

## Architecture

### Backend (Flask, Python)
- **RAG Pipeline**: Combines vector search (Qdrant) with LLM (Groq Llama 3 via LangChain)
- **Document Processing**: Chunking, embedding (SentenceTransformers, HuggingFace), version control
- **NER & Intent**: spaCy-based NER (custom HR labels), Sentence-BERT intent classifier
- **Speech**: Google Speech Recognition, MeloTTS for TTS
- **Database**: SQLite for users, conversations, documents, manual overrides
- **APIs**: REST endpoints for chat, upload, queries, chat logs, overrides, authentication, escalation, etc.
- **Manual Overrides**: Managed via `admin_dashboard/override_manager.py` and `overrides.db`

### Frontend (React, Tailwind, Framer Motion)
- **Admin Dashboard**: `admin_dashboard_ui/` (React)
  - View recent queries, chat logs, manual overrides
  - Upload HR documents
  - Responsive, modern UI
- **API Integration**: Fetches data from Flask backend at `localhost:5051`

---

## Data & Model Storage
- **Models**: Stored in `data/models/` (entity_extractor, intent_classifier)
- **Embeddings/Chunks**: `data/processed/` (JSON files for each document)
- **Databases**: `data/db/` (users.db, documents.db, convo.db, chatbot.db)
- **Manual Overrides**: `admin_dashboard/overrides.db` (SQLite, managed by override_manager.py)
- **Raw/Processed Data**: `data/raw/`, `data/processed/`, `data/raw_files/`
- **Model Caches**: `data/models_cache/`

---

## API Endpoints (Backend)
- `/api/query` (POST): Query the chatbot
- `/api/upload` (POST): Upload HR document
- `/api/queries` (GET): Recent queries (for dashboard)
- `/api/chatlogs` (GET): Recent chat logs (for dashboard)
- `/api/overrides` (GET): List manual overrides
- `/api/user` (GET): Get current user info
- `/api/register` (POST): Register user
- `/api/login` (POST): Login
- `/api/logout` (POST): Logout
- `/api/hr-representatives` (GET): List HR reps
- `/api/submit-escalation` (POST): Escalate to HR
- `/api/speech-to-text` (POST): Speech recognition
- `/api/start-training` (POST): Trigger document re-indexing
- `/api/summarize-document` (POST): Summarize document
- `/api/clear-history` (POST): Clear chat history
- `/api/file-preview` (GET): Preview uploaded file
- `/api/health/documents` (GET): Document health check
- `/api/process-hr-files` (POST): Process HR files
- `/api/chats/<chat_id>` (GET): Get chat messages
- `/api/chats/<chat_id>/count` (GET): Get message count

---

## Analytics API Contract

### /api/chat-analytics/live (GET)

Returns all analytics data for the dashboard in a single response. This is the only analytics endpoint used by the frontend.

**Response schema:**

```
{
  "total_queries": number,                // Total queries in the period
  "avg_sentiment": number,                // Average sentiment score
  "active_users": number,                 // Number of active users
  "unique_questions": number,             // Number of unique user questions
  "top_intents": [                        // Top user intents
    { "intent": string, "count": number }
  ],
  "trending_topics": [                    // Trending topics with time series
    { "topic": string, "trend": number[] }
  ],
  "sentiment_distribution": [             // Sentiment breakdown
    { "sentiment": string, "count": number }
  ],
  "top_questions": [                      // Most frequently asked questions
    { "question": string, "count": number }
  ]
}
```

- All keys are always present.
- This endpoint is the single source of truth for all analytics widgets in the admin dashboard.
- Legacy endpoints like /analytics/top-intents, /analytics/topic-trends, /analytics/sentiment-distribution, and /api/analytics/overview have been removed.

---

## Setup & Installation

### Backend
1. Clone the repo
2. Create a virtual environment:
   ```
   conda create -p venv python=3.10 -y
   conda activate venv/
   ```
3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Set up `.env` with API keys and config (see below)
5. Start backend:
   ```
   python app.py
   ```

### Frontend (Admin Dashboard)
1. Go to `admin_dashboard_ui/`
2. Install dependencies:
   ```
   npm install
   ```
3. Start dashboard:
   ```
   npm start
   ```
   (Runs on `localhost:3000` by default)

### Environment Variables
Create a `.env` file in the root with:
```
GROQ_API_KEY=your_groq_api_key
QDRANT_API_KEY=your_qdrant_key
QDRANT_URL=your_qdrant_url
# Email escalation (optional)
     SMTP_SERVER=smtp.gmail.com
     SMTP_PORT=587
     SMTP_USERNAME=<EMAIL>
     SMTP_PASSWORD=your_app_password
     SENDER_EMAIL=<EMAIL>
     HR_EMAILS=<EMAIL>,<EMAIL>
     ENABLE_EMAIL_ESCALATION=true
     ```

---

## Usage
- **Upload HR Docs**: Use dashboard or place files in `data/raw_files/`
- **Chat**: Use main UI (not included in dashboard) or API
- **Admin Dashboard**: View queries, logs, manage overrides, upload docs
- **Manual Overrides**: Set via dashboard (future), or directly in `overrides.db` using `override_manager.py`

---

## Sources & Dependencies
- **LLM**: Groq Llama 3 (via LangChain, API)
- **Embeddings**: SentenceTransformers (`multi-qa-mpnet-base-dot-v1`), HuggingFace
- **NER**: spaCy (custom HR model in `data/models/entity_extractor/`)
- **Intent**: Sentence-BERT, PyTorch (`data/models/intent_classifier/`)
- **Vector DB**: Qdrant Cloud
- **Speech**: Google Speech Recognition, MeloTTS (HuggingFace)
- **Frontend**: React, Tailwind, Framer Motion
- **Backend**: Flask, SQLite, SQLAlchemy, LangChain, etc.
- **Data Storage**: All persistent data/models in `data/` and `admin_dashboard/overrides.db`

---

## Project Structure
```
/ (root)
├── app.py                  # Flask backend
├── requirements.txt        # Backend dependencies
├── admin_dashboard_ui/     # React admin dashboard
├── admin_dashboard/        # Admin backend logic, overrides
├── data/                   # All models, embeddings, DBs, processed files
│   ├── models/             # NER, intent classifier
│   ├── processed/          # Embeddings, chunks
│   ├── db/                 # SQLite DBs
│   └── ...
├── static/                 # Static assets (CSS, JS, images)
├── templates/              # HTML templates
└── tests/                  # Unit/integration tests
```

---

## Testing
- Backend: `pytest tests/`
- Frontend: `npm test` in `admin_dashboard_ui/`

---

## Contributing
Pull requests welcome! Please open issues for bugs/feature requests.

---

## License
MIT
