from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from src.admin.models import AdminUser, Role, RolePermission, Permission
from src.database import get_db  # Adjust import if needed

# Dummy current user dependency (replace with real auth logic)
def get_current_admin(request: Request, db: Session = Depends(get_db)) -> AdminUser:
    # In production, extract user info from JWT/session
    # Here, assume user_id is set in request.state for demo
    user_id = getattr(request.state, 'user_id', None)
    if not user_id:
        raise HTTPException(status_code=401, detail="Not authenticated")
    user = db.query(AdminUser).filter(AdminUser.id == user_id).first()
    if not user:
        raise HTTPException(status_code=401, detail="User not found")
    return user

def require_permission(current_user: AdminUser, permission_key: str, db: Session):
    role = db.query(Role).filter(Role.id == current_user.role_id).first()
    if not role:
        raise HTTPException(status_code=403, detail="Role not found")
    role_permission = db.query(RolePermission).join(Permission).filter(
        RolePermission.role_id == role.id,
        Permission.key == permission_key
    ).first()
    if not role_permission:
        raise HTTPException(status_code=403, detail=f"Missing permission: {permission_key}")
    return True

def enforce_role_level(current_user: AdminUser, target_role_id: int, db: Session):
    current_role = db.query(Role).filter(Role.id == current_user.role_id).first()
    target_role = db.query(Role).filter(Role.id == target_role_id).first()
    if not current_role or not target_role:
        raise HTTPException(status_code=400, detail="Invalid role(s)")
    if target_role.level > current_role.level:
        raise HTTPException(status_code=403, detail="Cannot assign a role higher than your own.")
    return True 