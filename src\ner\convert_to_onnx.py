#!/usr/bin/env python3
"""
Simple ONNX conversion script for trained SpanMarker model.
"""

import os
import logging
import argparse
from pathlib import Path
from typing import Dict, Any

import torch
import numpy as np
from span_marker import SpanMarkerModel

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Optional import for ONNX conversion
try:
    from optimum.onnxruntime import ORTModelForTokenClassification
    OPTIMUM_AVAILABLE = True
except ImportError:
    OPTIMUM_AVAILABLE = False
    logger.warning("optimum library not available. ONNX conversion will not work.")

def convert_to_onnx(model_path: str, output_path: str = None) -> Dict[str, Any]:
    """
    Convert trained SpanMarker model to ONNX format.
    
    Args:
        model_path: Path to trained model
        output_path: Path to save ONNX model
    
    Returns:
        Dictionary with conversion results
    """
    if not OPTIMUM_AVAILABLE:
        return {
            "success": False,
            "error": "optimum library not available. Install with: pip install optimum[onnxruntime]"
        }
    
    try:
        logger.info(f"Loading trained model from: {model_path}")
        
        # Load the trained model
        model = SpanMarkerModel.from_pretrained(model_path)
        
        # Get model size before conversion
        model_size_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024**2)
        logger.info(f"Original model size: {model_size_mb:.2f} MB")
        
        # Set output path
        if output_path is None:
            output_path = str(Path(model_path).parent / "onnx_model")
        
        # Create output directory
        Path(output_path).mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Converting to ONNX format...")
        
        # Convert to ONNX using optimum
        ort_model = ORTModelForTokenClassification.from_pretrained(
            model_path,
            export=True,
            provider="CUDAExecutionProvider" if torch.cuda.is_available() else "CPUExecutionProvider"
        )
        
        # Save ONNX model
        ort_model.save_pretrained(output_path)
        
        # Get ONNX model size
        onnx_size_mb = sum(f.stat().st_size for f in Path(output_path).rglob("*") if f.is_file()) / (1024**2)
        
        logger.info(f"ONNX model saved to: {output_path}")
        logger.info(f"ONNX model size: {onnx_size_mb:.2f} MB")
        
        return {
            "success": True,
            "original_size_mb": model_size_mb,
            "onnx_size_mb": onnx_size_mb,
            "compression_ratio": model_size_mb / onnx_size_mb if onnx_size_mb > 0 else 0,
            "output_path": output_path
        }
        
    except Exception as e:
        logger.error(f"ONNX conversion failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }

def main():
    """Main conversion function."""
    parser = argparse.ArgumentParser(description="Convert SpanMarker model to ONNX")
    parser.add_argument("--model-path", type=str, 
                       default="data/models/ner_model",
                       help="Path to trained model")
    parser.add_argument("--output-path", type=str,
                       help="Path to save ONNX model (optional)")
    
    args = parser.parse_args()
    
    # Convert model
    result = convert_to_onnx(args.model_path, args.output_path)
    
    if result["success"]:
        print("\n" + "="*50)
        print("✅ ONNX CONVERSION COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"📁 Original model: {result['original_size_mb']:.2f} MB")
        print(f"📁 ONNX model: {result['onnx_size_mb']:.2f} MB")
        print(f"📊 Compression ratio: {result['compression_ratio']:.2f}x")
        print(f"📂 Output path: {result['output_path']}")
        print("="*50)
    else:
        print(f"\n❌ ONNX conversion failed: {result['error']}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 