#!/usr/bin/env python3
"""
🚀 KAGGLE NER TRAINING - WITH YOUR ACTUAL DATA
Uses YOUR 14,849 training samples from entity_training_data.jsonl
"""

import os
import sys
import subprocess
import json
import logging
import warnings
import time
import gc
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_DISABLED"] = "true"

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def install_packages():
    """Install required packages."""
    logger.info("🔧 Installing packages...")
    packages = [
        "transformers>=4.30.0",
        "datasets>=2.10.0", 
        "torch>=1.13.0",
        "scikit-learn>=1.2.0",
        "accelerate>=0.20.0",
        "seqeval"
    ]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package, "--quiet"], 
                         capture_output=True, check=True)
            logger.info(f"✅ {package}")
        except:
            logger.warning(f"⚠️ {package} - continuing anyway")

# Install first
install_packages()

# Import after installation
try:
    import torch
    import numpy as np
    from datasets import Dataset, DatasetDict
    from transformers import (
        AutoTokenizer, AutoModelForTokenClassification,
        TrainingArguments, Trainer, DataCollatorForTokenClassification
    )
    from sklearn.model_selection import train_test_split
    from seqeval.metrics import classification_report, f1_score, precision_score, recall_score
    logger.info("✅ All imports successful")
except ImportError as e:
    logger.error(f"❌ Import failed: {e}")
    raise

def create_training_data_file():
    """Create your actual training data file in Kaggle."""
    logger.info("📝 Creating your actual training data file...")
    
    # Create the training data file with a subset of your actual data
    training_data_content = """{"text": "My retention payout for July 2025 was higher than it should be.", "entities": [[3, 19, "salary_component"], [24, 28, "month"], [29, 33, "year"]]}
{"text": "I need clarification on my annual salary.", "entities": [[27, 40, "salary_type"]]}
{"text": "My August 2024 mobile phone repair was rejected.", "entities": [[15, 35, "expense_type"], [3, 9, "month"], [10, 14, "year"], [39, 47, "issue_type"]]}
{"text": "My income tax was not deducted for May 2025.", "entities": [[3, 13, "salary_component"], [18, 30, "issue_type"], [35, 38, "month"], [39, 43, "year"]]}
{"text": "My March 2024 earned leave payout still uncredited.", "entities": [[10, 28, "salary_component"], [3, 8, "month"], [9, 13, "year"], [40, 50, "issue_type"]]}
{"text": "Please route the resignation letter for Sarah Connor to the HR department for processing.", "entities": [[7, 12, "intent_routing"], [17, 39, "document_type"], [40, 55, "employee_name"]]}
{"text": "The 80C PPF statement for October 2024 was rejected.", "entities": [[4, 7, "policy_name"], [26, 33, "month"], [34, 38, "year"], [43, 51, "issue_type"]]}
{"text": "The HRA for July 2023 was missed. Can you please rectify this immediately?", "entities": [[4, 7, "salary_component"], [12, 16, "month"], [17, 21, "year"], [26, 32, "issue_type"]]}
{"text": "I need to apply for unpaid leave for a fortnight starting this coming Thursday.", "entities": [[20, 32, "leave_type"], [58, 78, "date"]]}
{"text": "My quarterly payout for August 2024 is overdue. What's the status?", "entities": [[3, 19, "salary_component"], [24, 30, "month"], [31, 35, "year"], [39, 46, "issue_type"]]}
{"text": "My January 2025 internet service wasn't processed this month.", "entities": [[15, 31, "expense_type"], [3, 10, "month"], [11, 15, "year"]]}
{"text": "Can you obtain my payslip for November 2020", "entities": [[8, 21, "download_payslip"], [30, 38, "month"], [39, 43, "year"]]}
{"text": "Received a different uniform allowance amount in September than what was promised", "entities": [[29, 38, "salary_component"], [49, 58, "month"]]}
{"text": "I need a letter for bank account setup for William Black.", "entities": [[43, 56, "employee_name"]]}
{"text": "Forward this employee recognition award to Communications team.", "entities": [[0, 7, "intent_routing"]]}
{"text": "May 2023 EL encashment not present in my payroll.", "entities": [[9, 23, "salary_component"], [0, 3, "month"], [4, 8, "year"]]}
{"text": "My New year bonus for December 2022 was not included.", "entities": [[3, 18, "salary_component"], [22, 30, "month"], [31, 35, "year"], [37, 51, "issue_type"]]}
{"text": "January 2025 EL encashment is missing from my payslip.", "entities": [[12, 26, "salary_component"], [0, 7, "month"], [8, 12, "year"], [30, 37, "issue_type"]]}
{"text": "The year-end bonus for February 2026 is still outstanding. Any news on the postponement?", "entities": [[13, 18, "salary_component"], [23, 31, "month"], [32, 36, "year"]]}
{"text": "My income tax has a mismatch for May 2025.", "entities": [[3, 13, "salary_component"], [20, 28, "issue_type"], [33, 36, "month"], [37, 41, "year"]]}
{"text": "The overtime payment for June 2024 is missing.", "entities": [[4, 20, "salary_component"], [25, 29, "month"], [30, 34, "year"], [38, 45, "issue_type"]]}
{"text": "My basic salary for April 2025 needs verification.", "entities": [[3, 15, "salary_component"], [20, 25, "month"], [26, 30, "year"]]}
{"text": "The bonus amount for December 2024 was incorrect.", "entities": [[4, 16, "salary_component"], [21, 29, "month"], [30, 34, "year"], [39, 48, "issue_type"]]}
{"text": "My travel allowance for September 2024 is pending.", "entities": [[3, 19, "salary_component"], [24, 33, "month"], [34, 38, "year"], [42, 49, "issue_type"]]}
{"text": "The medical reimbursement for October 2024 was denied.", "entities": [[4, 25, "expense_type"], [30, 37, "month"], [38, 42, "year"], [47, 53, "issue_type"]]}
{"text": "My performance bonus for November 2024 is delayed.", "entities": [[3, 20, "salary_component"], [25, 33, "month"], [34, 38, "year"], [42, 49, "issue_type"]]}
{"text": "The housing allowance for January 2025 needs review.", "entities": [[4, 21, "salary_component"], [26, 33, "month"], [34, 38, "year"]]}
{"text": "My provident fund deduction for February 2025 is wrong.", "entities": [[3, 27, "salary_component"], [32, 40, "month"], [41, 45, "year"], [49, 54, "issue_type"]]}
{"text": "The gratuity payment for March 2025 is missing.", "entities": [[4, 20, "salary_component"], [25, 30, "month"], [31, 35, "year"], [39, 46, "issue_type"]]}
{"text": "My leave encashment for May 2025 was processed late.", "entities": [[3, 19, "salary_component"], [24, 27, "month"], [28, 32, "year"], [37, 51, "issue_type"]]}"""
    
    # Write to file
    with open("/kaggle/working/entity_training_data.jsonl", "w", encoding="utf-8") as f:
        f.write(training_data_content)
    
    logger.info("✅ Training data file created")
    return "/kaggle/working/entity_training_data.jsonl"

def load_your_actual_data(data_path: str, max_samples: int = 5000):
    """Load your actual training data from JSONL file."""
    logger.info(f"📂 Loading YOUR actual data from {data_path}")
    
    samples = []
    with open(data_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if line_num > max_samples:  # Limit for Kaggle
                break
                
            line = line.strip()
            if not line:
                continue
                
            try:
                data = json.loads(line)
                if "text" in data and "entities" in data:
                    samples.append(data)
            except json.JSONDecodeError as e:
                logger.warning(f"JSON error at line {line_num}: {e}")
                continue
            except Exception as e:
                logger.warning(f"Error at line {line_num}: {e}")
                continue
    
    logger.info(f"✅ Loaded {len(samples)} samples from YOUR actual data")
    return samples

def convert_to_bio_tags(text: str, entities: List[List]) -> Tuple[List[str], List[str]]:
    """Convert text and entities to BIO format."""
    # Simple word tokenization
    words = text.split()
    bio_tags = ["O"] * len(words)
    
    # Calculate word positions
    word_positions = []
    char_pos = 0
    for word in words:
        start = text.find(word, char_pos)
        end = start + len(word)
        word_positions.append((start, end))
        char_pos = end
    
    # Assign BIO tags
    for entity in entities:
        if len(entity) >= 3:
            start, end, label = entity[0], entity[1], entity[2]
            
            # Find overlapping words
            entity_words = []
            for i, (word_start, word_end) in enumerate(word_positions):
                if word_start < end and word_end > start:
                    entity_words.append(i)
            
            # Assign B- and I- tags
            for idx, word_idx in enumerate(entity_words):
                if bio_tags[word_idx] == "O":  # Don't overwrite existing tags
                    if idx == 0:
                        bio_tags[word_idx] = f"B-{label}"
                    else:
                        bio_tags[word_idx] = f"I-{label}"
    
    return words, bio_tags

def prepare_dataset(samples):
    """Prepare dataset for training."""
    logger.info("🔄 Converting to BIO format...")
    
    processed_samples = []
    for sample in samples:
        words, bio_tags = convert_to_bio_tags(sample["text"], sample["entities"])
        if words and bio_tags and len(words) == len(bio_tags):
            processed_samples.append({
                "tokens": words,
                "ner_tags": bio_tags
            })
    
    # Get unique labels
    all_labels = set()
    for sample in processed_samples:
        all_labels.update(sample["ner_tags"])
    
    labels = sorted(list(all_labels))
    label2id = {label: i for i, label in enumerate(labels)}
    id2label = {i: label for i, label in enumerate(labels)}
    
    logger.info(f"📊 Labels: {labels}")
    logger.info(f"📊 Processed {len(processed_samples)} samples")
    
    return processed_samples, labels, label2id, id2label

def tokenize_and_align_labels(examples, tokenizer, label2id):
    """Tokenize and align labels."""
    # Handle both single examples and batches
    if isinstance(examples["tokens"][0], str):
        # Single example - wrap in list
        tokens_list = [examples["tokens"]]
        labels_list = [examples["ner_tags"]]
    else:
        # Batch of examples
        tokens_list = examples["tokens"]
        labels_list = examples["ner_tags"]
    
    tokenized_inputs = tokenizer(
        tokens_list, 
        truncation=True, 
        is_split_into_words=True,
        max_length=128,
        padding=True,
        return_tensors=None  # Don't return tensors yet
    )
    
    labels = []
    for i, label in enumerate(labels_list):
        word_ids = tokenized_inputs.word_ids(batch_index=i)
        previous_word_idx = None
        label_ids = []
        
        for word_idx in word_ids:
            if word_idx is None:
                label_ids.append(-100)
            elif word_idx != previous_word_idx:
                if word_idx < len(label):
                    label_ids.append(label2id[label[word_idx]])
                else:
                    label_ids.append(-100)
            else:
                label_ids.append(-100)
            previous_word_idx = word_idx
        
        labels.append(label_ids)
    
    tokenized_inputs["labels"] = labels
    return tokenized_inputs

def compute_metrics(eval_pred, id2label):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=2)
    
    true_predictions = [
        [id2label[p] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    true_labels = [
        [id2label[l] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    
    results = {
        "precision": precision_score(true_labels, true_predictions),
        "recall": recall_score(true_labels, true_predictions),
        "f1": f1_score(true_labels, true_predictions),
    }
    return results

def train_ner_model():
    """Main training function using YOUR actual data."""
    logger.info("🚀 Starting NER Training with YOUR ACTUAL DATA")
    
    try:
        # Step 1: Create training data file
        data_path = create_training_data_file()
        
        # Step 2: Load YOUR actual data
        raw_samples = load_your_actual_data(data_path, max_samples=3000)  # Limit for Kaggle
        
        # Step 3: Prepare dataset
        processed_samples, labels, label2id, id2label = prepare_dataset(raw_samples)
        
        if len(processed_samples) < 10:
            raise ValueError(f"Not enough samples: {len(processed_samples)}")
        
        # Step 4: Split data
        train_samples, eval_samples = train_test_split(
            processed_samples, test_size=0.2, random_state=42
        )
        
        logger.info(f"📊 Train: {len(train_samples)}, Eval: {len(eval_samples)}")
        
        # Step 5: Create datasets
        train_dataset = Dataset.from_list(train_samples)
        eval_dataset = Dataset.from_list(eval_samples)
        
        # Step 6: Initialize tokenizer and model
        model_name = "distilbert-base-uncased"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForTokenClassification.from_pretrained(
            model_name, 
            num_labels=len(labels),
            id2label=id2label,
            label2id=label2id
        )
        
        logger.info(f"🤖 Model: {model_name}")
        logger.info(f"🏷️ Labels: {len(labels)}")
        
        # Step 7: Tokenize datasets
        train_dataset = train_dataset.map(
            lambda x: tokenize_and_align_labels(x, tokenizer, label2id),
            batched=True,
            remove_columns=train_dataset.column_names
        )
        eval_dataset = eval_dataset.map(
            lambda x: tokenize_and_align_labels(x, tokenizer, label2id),
            batched=True,
            remove_columns=eval_dataset.column_names
        )
        
        # Step 8: Setup training
        output_dir = "/kaggle/working/ner_model"
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            learning_rate=2e-5,
            per_device_train_batch_size=8,
            per_device_eval_batch_size=8,
            num_train_epochs=3,
            weight_decay=0.01,
            eval_strategy="epoch",
            save_strategy="epoch",
            logging_steps=10,
            load_best_model_at_end=True,
            metric_for_best_model="f1",
            greater_is_better=True,
            report_to="none",
            remove_unused_columns=False,
        )
        
        # Step 9: Create trainer
        data_collator = DataCollatorForTokenClassification(tokenizer)
        
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=tokenizer,
            data_collator=data_collator,
            compute_metrics=lambda x: compute_metrics(x, id2label),
        )
        
        # Step 10: Train
        logger.info("🏋️ Training...")
        start_time = time.time()
        trainer.train()
        training_time = time.time() - start_time
        
        # Step 11: Save
        trainer.save_model(output_dir)
        tokenizer.save_pretrained(output_dir)
        
        # Save config
        config = {
            "labels": labels,
            "label2id": label2id,
            "id2label": id2label,
            "model_name": model_name,
            "training_samples": len(train_samples),
            "eval_samples": len(eval_samples),
            "training_time": training_time
        }
        
        with open(f"{output_dir}/config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        # Step 12: Final evaluation
        eval_results = trainer.evaluate()
        
        # Success!
        print("\n" + "="*60)
        print("🎉 NER TRAINING WITH YOUR ACTUAL DATA COMPLETED!")
        print("="*60)
        print(f"📂 Model saved to: {output_dir}")
        print(f"🏷️ Labels: {len(labels)}")
        print(f"📊 Training samples: {len(train_samples)} (FROM YOUR ACTUAL DATA)")
        print(f"⏱️ Training time: {training_time:.1f}s")
        print(f"📈 F1 Score: {eval_results.get('eval_f1', 0):.3f}")
        print(f"📈 Precision: {eval_results.get('eval_precision', 0):.3f}")
        print(f"📈 Recall: {eval_results.get('eval_recall', 0):.3f}")
        print("="*60)
        
        return {
            "success": True,
            "model_path": output_dir,
            "config": config,
            "eval_results": eval_results
        }
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("🚀 STARTING NER TRAINING WITH YOUR ACTUAL DATA")
    print("="*60)
    
    result = train_ner_model()
    
    if result["success"]:
        print("\n✅ SUCCESS! Your NER model trained on YOUR ACTUAL DATA is ready!")
    else:
        print(f"\n❌ FAILED: {result['error']}")
