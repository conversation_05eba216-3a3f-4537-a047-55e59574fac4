#!/usr/bin/env python3
"""
🚀 COMPLETE KAGGLE NER TRAINING - SELF-CONTAINED
Based on your actual codebase - NO EXTERNAL IMPORTS
"""

import os
import sys
import subprocess
import json
import logging
import warnings
import time
import gc
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Suppress all warnings
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"

# Setup logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def install_packages():
    """Install required packages."""
    logger.info("🔧 Installing required packages...")
    
    packages = [
        "span-marker",
        "transformers>=4.30.0",
        "datasets>=2.10.0", 
        "scikit-learn>=1.2.0",
        "accelerate>=0.20.0"
    ]
    
    for package in packages:
        try:
            logger.info(f"📦 Installing {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ {package} installed successfully")
            else:
                logger.warning(f"⚠️ {package} installation had warnings")
                
        except Exception as e:
            logger.error(f"❌ Failed to install {package}: {e}")

# Install packages first
install_packages()

# Now import the packages
try:
    import torch
    import numpy as np
    from datasets import Dataset
    from sklearn.model_selection import train_test_split
    from span_marker import SpanMarkerModel, Trainer as SpanMarkerTrainer
    from transformers import TrainingArguments, EarlyStoppingCallback
    from collections import Counter
    logger.info("✅ All packages imported successfully")
except ImportError as e:
    logger.error(f"❌ Import failed: {e}")
    raise

def create_training_data_from_your_format():
    """Create training data using YOUR actual data format."""
    logger.info("📝 Creating training data using your format...")
    
    # Your actual training data format from entity_training_data.jsonl
    training_samples = [
        {"text": "My retention payout for July 2025 was higher than it should be.", "entities": [[3, 19, "salary_component"], [24, 28, "month"], [29, 33, "year"]]},
        {"text": "I need clarification on my annual salary.", "entities": [[27, 40, "salary_type"]]},
        {"text": "My August 2024 mobile phone repair was rejected.", "entities": [[15, 35, "expense_type"], [3, 9, "month"], [10, 14, "year"], [39, 47, "issue_type"]]},
        {"text": "My income tax was not deducted for May 2025.", "entities": [[3, 13, "salary_component"], [18, 30, "issue_type"], [35, 38, "month"], [39, 43, "year"]]},
        {"text": "My March 2024 earned leave payout still uncredited.", "entities": [[10, 28, "salary_component"], [3, 8, "month"], [9, 13, "year"], [40, 50, "issue_type"]]},
        {"text": "The overtime payment for June 2024 is missing.", "entities": [[4, 20, "salary_component"], [25, 29, "month"], [30, 34, "year"], [38, 45, "issue_type"]]},
        {"text": "My basic salary for April 2025 needs verification.", "entities": [[3, 15, "salary_component"], [20, 25, "month"], [26, 30, "year"]]},
        {"text": "The bonus amount for December 2024 was incorrect.", "entities": [[4, 16, "salary_component"], [21, 29, "month"], [30, 34, "year"], [39, 48, "issue_type"]]},
        {"text": "My travel allowance for September 2024 is pending.", "entities": [[3, 19, "salary_component"], [24, 33, "month"], [34, 38, "year"], [42, 49, "issue_type"]]},
        {"text": "The medical reimbursement for October 2024 was denied.", "entities": [[4, 25, "expense_type"], [30, 37, "month"], [38, 42, "year"], [47, 53, "issue_type"]]},
        {"text": "My performance bonus for November 2024 is delayed.", "entities": [[3, 20, "salary_component"], [25, 33, "month"], [34, 38, "year"], [42, 49, "issue_type"]]},
        {"text": "The housing allowance for January 2025 needs review.", "entities": [[4, 21, "salary_component"], [26, 33, "month"], [34, 38, "year"]]},
        {"text": "My provident fund deduction for February 2025 is wrong.", "entities": [[3, 27, "salary_component"], [32, 40, "month"], [41, 45, "year"], [49, 54, "issue_type"]]},
        {"text": "The gratuity payment for March 2025 is missing.", "entities": [[4, 20, "salary_component"], [25, 30, "month"], [31, 35, "year"], [39, 46, "issue_type"]]},
        {"text": "My leave encashment for May 2025 was processed late.", "entities": [[3, 19, "salary_component"], [24, 27, "month"], [28, 32, "year"], [37, 51, "issue_type"]]},
    ]
    
    # Expand the dataset by creating variations
    expanded_samples = []
    
    # Add original samples
    expanded_samples.extend(training_samples)
    
    # Create variations with different months/years
    months = ["January", "February", "March", "April", "May", "June", 
              "July", "August", "September", "October", "November", "December"]
    years = ["2023", "2024", "2025"]
    
    # Generate more samples by varying months and years
    base_templates = [
        {"text": "My {component} for {month} {year} needs attention.", "entities": [[3, "{component_end}", "salary_component"], ["{month_start}", "{month_end}", "month"], ["{year_start}", "{year_end}", "year"]]},
        {"text": "The {component} for {month} {year} is {issue}.", "entities": [[4, "{component_end}", "salary_component"], ["{month_start}", "{month_end}", "month"], ["{year_start}", "{year_end}", "year"], ["{issue_start}", "{issue_end}", "issue_type"]]},
        {"text": "My {expense} for {month} {year} was {issue}.", "entities": [[3, "{expense_end}", "expense_type"], ["{month_start}", "{month_end}", "month"], ["{year_start}", "{year_end}", "year"], ["{issue_start}", "{issue_end}", "issue_type"]]},
    ]
    
    components = ["basic salary", "overtime pay", "bonus amount", "allowance", "deduction"]
    expenses = ["travel expense", "medical bill", "phone bill", "internet cost"]
    issues = ["pending", "delayed", "missing", "incorrect", "denied"]
    
    # Generate additional samples
    for i in range(50):  # Generate 50 more samples
        template = base_templates[i % len(base_templates)]
        month = months[i % len(months)]
        year = years[i % len(years)]
        
        if "component" in template["text"]:
            component = components[i % len(components)]
            issue = issues[i % len(issues)]
            
            text = template["text"].format(component=component, month=month, year=year, issue=issue)
            
            # Calculate positions
            component_start = text.find(component)
            component_end = component_start + len(component)
            month_start = text.find(month)
            month_end = month_start + len(month)
            year_start = text.find(year)
            year_end = year_start + len(year)
            issue_start = text.find(issue)
            issue_end = issue_start + len(issue)
            
            entities = []
            if component_start >= 0:
                entities.append([component_start, component_end, "salary_component"])
            if month_start >= 0:
                entities.append([month_start, month_end, "month"])
            if year_start >= 0:
                entities.append([year_start, year_end, "year"])
            if issue_start >= 0:
                entities.append([issue_start, issue_end, "issue_type"])
            
            expanded_samples.append({"text": text, "entities": entities})
        
        elif "expense" in template["text"]:
            expense = expenses[i % len(expenses)]
            issue = issues[i % len(issues)]
            
            text = template["text"].format(expense=expense, month=month, year=year, issue=issue)
            
            # Calculate positions
            expense_start = text.find(expense)
            expense_end = expense_start + len(expense)
            month_start = text.find(month)
            month_end = month_start + len(month)
            year_start = text.find(year)
            year_end = year_start + len(year)
            issue_start = text.find(issue)
            issue_end = issue_start + len(issue)
            
            entities = []
            if expense_start >= 0:
                entities.append([expense_start, expense_end, "expense_type"])
            if month_start >= 0:
                entities.append([month_start, month_end, "month"])
            if year_start >= 0:
                entities.append([year_start, year_end, "year"])
            if issue_start >= 0:
                entities.append([issue_start, issue_end, "issue_type"])
            
            expanded_samples.append({"text": text, "entities": entities})
    
    logger.info(f"✅ Created {len(expanded_samples)} training samples")
    return expanded_samples

def validate_entity_spans(text: str, entities: List[List], line_num: int) -> List[List]:
    """Validate entity spans and return valid entities."""
    valid_entities = []
    for entity in entities:
        try:
            if len(entity) >= 3:
                start, end, label = entity[0], entity[1], entity[2]
                
                # Validate span boundaries
                if not (0 <= start < end <= len(text)):
                    logger.warning(f"Invalid span [{start}, {end}] for text length {len(text)} at line {line_num}")
                    continue
                
                # Validate label
                if not isinstance(label, str) or not label.strip():
                    logger.warning(f"Invalid label '{label}' at line {line_num}")
                    continue
                
                valid_entities.append(entity)
            else:
                logger.warning(f"Invalid entity format at line {line_num}: {entity}")
        except Exception as e:
            logger.warning(f"Error validating entity {entity} at line {line_num}: {e}")
    
    return valid_entities

def convert_to_bio_format(samples):
    """Convert samples to BIO format for SpanMarker."""
    logger.info("🔄 Converting to BIO format...")
    
    bio_samples = []
    
    for idx, sample in enumerate(samples):
        text = sample["text"]
        entities = sample["entities"]
        
        # Validate entities first
        valid_entities = validate_entity_spans(text, entities, idx + 1)
        
        # Simple whitespace tokenization
        tokens = text.split()
        ner_tags = ["O"] * len(tokens)
        
        # Calculate token positions
        token_positions = []
        char_pos = 0
        for token in tokens:
            token_start = text.find(token, char_pos)
            if token_start == -1:
                while char_pos < len(text) and text[char_pos].isspace():
                    char_pos += 1
                token_start = char_pos
            token_end = token_start + len(token)
            token_positions.append((token_start, token_end))
            char_pos = token_end
        
        # Process entities and assign BIO tags
        for entity in valid_entities:
            try:
                start, end, label = int(entity[0]), int(entity[1]), str(entity[2])
                
                # Find tokens that overlap with this entity
                entity_tokens = []
                for i, (token_start, token_end) in enumerate(token_positions):
                    if token_start < end and token_end > start:
                        entity_tokens.append(i)
                
                # Assign BIO tags
                for idx, token_idx in enumerate(entity_tokens):
                    if ner_tags[token_idx] == "O":  # Only assign if not already tagged
                        if idx == 0:
                            ner_tags[token_idx] = f"B-{label}"
                        else:
                            ner_tags[token_idx] = f"I-{label}"
            except (ValueError, IndexError, TypeError):
                continue
        
        # Only add if we have valid tokens and tags
        if tokens and ner_tags and len(tokens) == len(ner_tags):
            bio_samples.append({
                "tokens": tokens,
                "ner_tags": ner_tags
            })
    
    logger.info(f"✅ Converted {len(bio_samples)} samples to BIO format")
    return bio_samples

def get_optimal_batch_size():
    """Get optimal batch size for current environment."""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        gpu_name = torch.cuda.get_device_name(0)
        logger.info(f"🚀 GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        
        if gpu_memory >= 15:  # P100 or better
            return 4
        elif gpu_memory >= 8:   # T4
            return 2
        else:
            return 1
    else:
        logger.info("💻 Using CPU")
        return 1

def train_ner_model():
    """Main training function using your exact data format."""
    logger.info("🚀 Starting NER Model Training with Your Data Format")
    
    try:
        # Step 1: Create training data using your format
        raw_samples = create_training_data_from_your_format()
        
        # Step 2: Convert to BIO format
        bio_samples = convert_to_bio_format(raw_samples)
        
        if len(bio_samples) < 20:
            raise ValueError(f"Insufficient training samples: {len(bio_samples)}")
        
        # Step 3: Split data
        train_data, eval_data = train_test_split(
            bio_samples, 
            test_size=0.2, 
            random_state=42
        )
        
        logger.info(f"📊 Train samples: {len(train_data)}")
        logger.info(f"📊 Eval samples: {len(eval_data)}")
        
        # Step 4: Extract labels
        all_labels = set()
        for sample in train_data + eval_data:
            for tag in sample["ner_tags"]:
                if tag != "O":
                    all_labels.add(tag)
        
        labels = sorted(list(all_labels))
        logger.info(f"🏷️ Entity labels: {labels}")
        
        if not labels:
            raise ValueError("No valid entity labels found!")
        
        # Step 5: Create datasets
        train_dataset = Dataset.from_list(train_data)
        eval_dataset = Dataset.from_list(eval_data)
        
        # Step 6: Initialize model
        logger.info("🤖 Initializing SpanMarker model...")
        
        model = SpanMarkerModel.from_pretrained(
            "microsoft/deberta-v3-base",
            labels=labels,
            model_max_length=256,
            entity_max_length=8
        )
        
        # Move to GPU if available
        if torch.cuda.is_available():
            model = model.cuda()
            logger.info("🚀 Model moved to GPU")
        
        # Step 7: Setup training
        output_dir = Path("/kaggle/working/ner_model")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        batch_size = get_optimal_batch_size()
        logger.info(f"📦 Using batch size: {batch_size}")
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=str(output_dir),
            learning_rate=5e-5,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            num_train_epochs=3,
            warmup_ratio=0.1,
            weight_decay=0.01,
            fp16=torch.cuda.is_available(),
            save_strategy="epoch",
            eval_strategy="epoch",
            logging_steps=10,
            save_total_limit=2,
            load_best_model_at_end=True,
            metric_for_best_model="eval_f1",
            greater_is_better=True,
            report_to="none",
            dataloader_num_workers=0,
            remove_unused_columns=False,
            disable_tqdm=False,
        )
        
        # Step 8: Create trainer
        trainer = SpanMarkerTrainer(
            model=model,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            args=training_args,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
        )
        
        # Step 9: Train
        logger.info("🏋️ Starting training...")
        start_time = time.time()
        
        trainer.train()
        
        training_time = time.time() - start_time
        logger.info(f"✅ Training completed in {training_time:.1f} seconds")
        
        # Step 10: Save model
        logger.info("💾 Saving model...")
        trainer.save_model(str(output_dir))
        
        # Save entity types
        entity_types_file = output_dir / "entity_types.json"
        with open(entity_types_file, 'w', encoding='utf-8') as f:
            json.dump({
                "entity_types": labels,
                "model_info": {
                    "base_model": "microsoft/deberta-v3-base",
                    "training_samples": len(train_data),
                    "eval_samples": len(eval_data),
                    "training_time": training_time
                }
            }, f, indent=2)
        
        # Step 11: Final evaluation
        logger.info("📊 Running final evaluation...")
        eval_results = trainer.evaluate()
        
        # Step 12: Cleanup
        del model, trainer
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        # Success message
        logger.info("🎉 Training completed successfully!")
        
        print("\n" + "="*60)
        print("🎉 NER MODEL TRAINING COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"📂 Model saved to: {output_dir}")
        print(f"🏷️ Entity types: {len(labels)} ({', '.join(labels)})")
        print(f"📊 Training samples: {len(train_data)}")
        print(f"📊 Evaluation samples: {len(eval_data)}")
        print(f"⏱️ Training time: {training_time:.1f} seconds")
        print(f"📈 Final F1 Score: {eval_results.get('eval_f1', 0):.3f}")
        print(f"📈 Final Precision: {eval_results.get('eval_precision', 0):.3f}")
        print(f"📈 Final Recall: {eval_results.get('eval_recall', 0):.3f}")
        print("="*60)
        
        return {
            "success": True,
            "model_path": str(output_dir),
            "entity_types": labels,
            "training_samples": len(train_data),
            "eval_samples": len(eval_data),
            "training_time": training_time,
            "eval_results": eval_results
        }
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

# Run the training
if __name__ == "__main__":
    print("🚀 STARTING KAGGLE NER TRAINING WITH YOUR DATA FORMAT")
    print("="*60)
    
    result = train_ner_model()
    
    if result["success"]:
        print("\n✅ SUCCESS! Your NER model is ready to use!")
        print(f"📂 Find your model at: {result['model_path']}")
    else:
        print(f"\n❌ FAILED: {result['error']}")
